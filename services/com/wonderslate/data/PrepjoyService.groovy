package com.wonderslate.data

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.groups.GroupsMst
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstManagerService
import com.wonderslate.institute.InstituteService
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.logs.LogsService
import com.wonderslate.prepjoy.DailyExamGroup
import com.wonderslate.prepjoy.DailyTestsDtl
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.prepjoy.QuizRanks
import com.wonderslate.prepjoy.QuizRecDtl
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.prepjoy.QuizStatistics
import com.wonderslate.prepjoy.UserMedals

import com.wonderslate.prepjoy.UserPointsPrepJoy
import com.wonderslate.publish.Blogs
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.ExamMst
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.AnalyticsService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.usermanagement.WinGenerator
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.sql.Sql
import org.grails.web.json.JSONArray

import java.text.ParseException
import java.text.SimpleDateFormat

@Transactional
class PrepjoyService {
    static ArrayList<String> names=new ArrayList<String>()
    static ArrayList<String> places=new ArrayList<String>()
    def springSecurityService
    def redisService
    def grailsApplication
    UtilService utilService
    DataProviderService dataProviderService
    AnalyticsService analyticsService
    AsyncLogsService asyncLogsService
    InstituteService instituteService
    LogsService logsService
    InstManagerService instManagerService
    
    def  static populateNamesAndPlaces() {
        names.add("Aadesh")
        names.add("Aadrik")
        names.add("Aakash")
        names.add("Aahan")
        names.add("Aarav")
        names.add("Aarnik")
        names.add("Abhay")
        names.add("Abhimanyu")
        names.add("Abhinav")
        names.add("Adit")
        names.add("Adhyan")
        names.add("Adrith")
        names.add("Advaith")
        names.add("Advik")
        names.add("Agastya")
        names.add("Ajay")
        names.add("Akshaj")
        names.add("Akshant")
        names.add("Akshat")
        names.add("Akshay")
        names.add("Amandeep")
        names.add("Amey")
        names.add("Anik")
        names.add("Aniket")
        names.add("Anishk")
        names.add("Ankit")
        names.add("Ansh")
        names.add("Anshul")
        names.add("Anvit")
        names.add("Arijit")
        names.add("Arjun")
        names.add("Arnav")
        names.add("Aruj")
        names.add("Arush")
        names.add("Aryan")
        names.add("Ashok")
        names.add("Ashvik")
        names.add("Atharv")
        names.add("Avaneesh")
        names.add("Avik")
        names.add("Aviraj")
        names.add("Avyan")
        names.add("Avyukt")
        names.add("Ayaan")
        names.add("Ayansh")
        names.add("Ayush")
        names.add("Bharat")
        names.add("Bhargav")
        names.add("Chandan")
        names.add("Chirag")
        names.add("Chetan")
        names.add("Daiwik")
        names.add("Daksh")
        names.add("Dilip")
        names.add("Darsh")
        names.add("Darshil")
        names.add("Deepak")
        names.add("Dev")
        names.add("Devansh")
        names.add("Dhruv")
        names.add("Dhruvam")
        names.add("Dipankar")
        names.add("Divyansh")
        names.add("Durjoy")
        names.add("Eklavya")
        names.add("Eshaanth")
        names.add("Gaurav")
        names.add("Gautam")
        names.add("Govinda")
        names.add("Gurdeep")
        names.add("Hari")
        names.add("Harish")
        names.add("Harsh")
        names.add("Harshad")
        names.add("Hemant")
        names.add("Himansh")
        names.add("Himanshu")
        names.add("Hitansh")
        names.add("Hiten")
        names.add("Hridhaan")
        names.add("Hrithik")
        names.add("Indra")
        names.add("Ishaan")
        names.add("Ishank")
        names.add("Ivaan")
        names.add("Jagan")
        names.add("Jai")
        names.add("Jatin")
        names.add("Jeet")
        names.add("Jyotiraditya")
        names.add("Kabir")
        names.add("Kairav")
        names.add("Kamal")
        names.add("Kanishk")
        names.add("Karan")
        names.add("Karthik")
        names.add("Kapil")
        names.add("Kavan")
        names.add("Kavyansh")
        names.add("Kiaan")
        names.add("Kiyansh")
        names.add("Krish")
        names.add("Kshitij")
        names.add("Kunal")
        names.add("Kush")
        names.add("Kushal")
        names.add("Lakshya")
        names.add("Lakshit")
        names.add("Lalit")
        names.add("Lavish")
        names.add("Madhavaditya")
        names.add("Madhavan")
        names.add("Malhar")
        names.add("Manish")
        names.add("Manoj")
        names.add("Manvik")
        names.add("Mayank")
        names.add("Mayukh")
        names.add("Mayuresh")
        names.add("Mehul")
        names.add("Mihir")
        names.add("Miransh")
        names.add("Mitansh")
        names.add("Mohan")
        names.add("Nakul")
        names.add("Naitik")
        names.add("Naksh")
        names.add("Naman")
        names.add("Navin")
        names.add("Nayan")
        names.add("Neil")
        names.add("Nihal")
        names.add("Nikit")
        names.add("Nikshith")
        names.add("Nikunj")
        names.add("Nirav")
        names.add("Nirved")
        names.add("Nishant")
        names.add("Nitesh")
        names.add("Nivan")
        names.add("Om")
        names.add("Omkar")
        names.add("Pankaj")
        names.add("Parag")
        names.add("Parikshit")
        names.add("Parth")
        names.add("Parthik")
        names.add("Parthiv")
        names.add("Pranav")
        names.add("Praneeth")
        names.add("Pransh")
        names.add("Prashant")
        names.add("Prateek")
        names.add("Pratham")
        names.add("Pratyush")
        names.add("Prem")
        names.add("Pritam")
        names.add("Priyanshu")
        names.add("Priyom")
        names.add("Pulkit")
        names.add("Purav")
        names.add("Raghav")
        names.add("Rahul")
        names.add("Rajesh")
        names.add("Rajiv")
        names.add("Rajvir")
        names.add("Rakesh")
        names.add("Ramesh")
        names.add("Ranveer")
        names.add("Rajat")
        names.add("Raunak")
        names.add("Reyansh")
        names.add("Riddhiman")
        names.add("Rihan")
        names.add("Rishabh")
        names.add("Rishaan")
        names.add("Rishaank")
        names.add("Rishit")
        names.add("Ritesh")
        names.add("Rituraj")
        names.add("Rivaan")
        names.add("Rohan")
        names.add("Rohit")
        names.add("Ronav")
        names.add("Ronit")
        names.add("Rudra")
        names.add("Sabhya")
        names.add("Sachin")
        names.add("Sachit")
        names.add("Sai")
        names.add("Sahil")
        names.add("Samar")
        names.add("Samarth")
        names.add("Samrat")
        names.add("Sanjay")
        names.add("Sarthak")
        names.add("Sarvin")
        names.add("Sathvik")
        names.add("Shakti")
        names.add("Shamit")
        names.add("Sharvil")
        names.add("Shashank")
        names.add("Shaurya")
        names.add("Shayan")
        names.add("Shivaay")
        names.add("Shivansh")
        names.add("Shivin")
        names.add("Shlok")
        names.add("Shreyan")
        names.add("Shreyansh")
        names.add("Shreyas")
        names.add("Siddharth")
        names.add("Soham")
        names.add("Spandan")
        names.add("Sriansh")
        names.add("Sriyan")
        names.add("Sujal")
        names.add("Sumedh")
        names.add("Tanay")
        names.add("Tanmay")
        names.add("Tarak")
        names.add("Tejas")
        names.add("Tushar")
        names.add("Utkarsh")
        names.add("Vaibhav")
        names.add("Vansh")
        names.add("Varun")
        names.add("Vatsal")
        names.add("Vedanth")
        names.add("Veer")
        names.add("Vehant")
        names.add("Vihaan")
        names.add("Vijay")
        names.add("Vinay")
        names.add("Vinod")
        names.add("Viraj")
        names.add("Virat")
        names.add("Viren")
        names.add("Vishal")
        names.add("Vishesh")
        names.add("Vivaan")
        names.add("Vivek")
        names.add("Vyas")
        names.add("Vyom")
        names.add("Yakshit")
        names.add("Yash")
        names.add("Yashvir")
        names.add("Yuvaan")
        names.add("Yuvraj")
        names.add("Aabha")
        names.add("Aahna")
        names.add("Aakanksha")
        names.add("Aakriti")
        names.add("Aalia")
        names.add("Aanya")
        names.add("Aarna")
        names.add("Aarohi")
        names.add("Aarushi")
        names.add("Aashi")
        names.add("Aayushi")
        names.add("Aditi")
        names.add("Advika")
        names.add("Adweta")
        names.add("Adya")
        names.add("Aisha")
        names.add("Akshara")
        names.add("Akshita")
        names.add("Ahana")
        names.add("Alekhya")
        names.add("Amaira")
        names.add("Amaya")
        names.add("Amna")
        names.add("Amrita")
        names.add("Amulya")
        names.add("Anaisha")
        names.add("Anika")
        names.add("Archana")
        names.add("Arunima")
        names.add("Avantika")
        names.add("Avya")
        names.add("Bandita")
        names.add("Banhi")
        names.add("Barkha")
        names.add("Bavishni")
        names.add("Bhakti")
        names.add("Bhanu")
        names.add("Bhrithi")
        names.add("Bimala")
        names.add("Bina")
        names.add("Bindhiya")
        names.add("Binita")
        names.add("Bodhi")
        names.add("Brinda")
        names.add("Chaitali")
        names.add("Chakrika")
        names.add("Chakshini")
        names.add("Chandni")
        names.add("Charita")
        names.add("Chavvi")
        names.add("Chetana")
        names.add("Cheshta")
        names.add("Chhaya")
        names.add("Chitrita")
        names.add("Chinmayi")
        names.add("Daksha")
        names.add("Dalaja")
        names.add("Damini")
        names.add("Damyanti")
        names.add("Darika")
        names.add("Darpana")
        names.add("Darsha")
        names.add("Darshita")
        names.add("Dayamai")
        names.add("Dayanita")
        names.add("Dayita")
        names.add("Debanjali")
        names.add("Deeksha")
        names.add("Deepali")
        names.add("Dhanishka")
        names.add("Dipanwita")
        names.add("Dipti")
        names.add("Diti")
        names.add("Divya")
        names.add("Diya")
        names.add("Drishti")
        names.add("Eesha")
        names.add("Eeshta")
        names.add("Ekaja")
        names.add("Ekani")
        names.add("Ekantika")
        names.add("Ekta")
        names.add("Ela")
        names.add("Eshana")
        names.add("Eswari")
        names.add("Eta")
        names.add("Falak")
        names.add("Falguni")
        names.add("Farida")
        names.add("Gagana")
        names.add("Gangotri")
        names.add("Ganishka")
        names.add("Gargi")
        names.add("Garima")
        names.add("Gaurika")
        names.add("Gautami")
        names.add("Gavya")
        names.add("Gayatri")
        names.add("Geetika")
        names.add("Gitanjali")
        names.add("Gulika")
        names.add("Haarika")
        names.add("Hamsika")
        names.add("Harini")
        names.add("Harita")
        names.add("Harisha")
        names.add("Harleen")
        names.add("Harshita")
        names.add("Hemani")
        names.add("Heena")
        names.add("Himani")
        names.add("Hiranya")
        names.add("Idika")
        names.add("Ila")
        names.add("Ikshita")
        names.add("Ina")
        names.add("Indali")
        names.add("Ira")
        names.add("Indira")
        names.add("Indu")
        names.add("Iniya")
        names.add("Ishaani")
        names.add("Ishita")
        names.add("Jaanvi")
        names.add("Jagruti")
        names.add("Jagvi")
        names.add("Januja")
        names.add("Janya")
        names.add("Jasmit")
        names.add("Jaya")
        names.add("Jeevika")
        names.add("Jhalak")
        names.add("Juhi")
        names.add("Kaira")
        names.add("Kajal")
        names.add("Kalpana")
        names.add("Kalyani")
        names.add("Kamala")
        names.add("Kanchana")
        names.add("Kangana")
        names.add("Kanti")
        names.add("Karishma")
        names.add("Kashika")
        names.add("Kashvi")
        names.add("Kavya")
        names.add("Kuhu")
        names.add("Kushi")
        names.add("Lata")
        names.add("Lavanya")
        names.add("Laboni")
        names.add("Lalita")
        names.add("Lasya")
        names.add("Lekha")
        names.add("Leena")
        names.add("Leya")
        names.add("Lipi")
        names.add("Lipika")
        names.add("Lopa")
        names.add("Maahi")
        names.add("Maanasi")
        names.add("Maanyata")
        names.add("Mahika")
        names.add("Manya")
        names.add("Madhu")
        names.add("Megha")
        names.add("Meher")
        names.add("Mitali")
        names.add("Mohini")
        names.add("Mukta")
        names.add("Nadira")
        names.add("Naira")
        names.add("Neeta")
        names.add("Neha")
        names.add("Netra")
        names.add("Nidhi")
        names.add("Nisha")
        names.add("Nitara")
        names.add("Noor")
        names.add("Oishi")
        names.add("Oorja")
        names.add("Ojasvi")
        names.add("Omaja")
        names.add("Omisha")
        names.add("Oni")
        names.add("Oorvi")
        names.add("Osha")
        names.add("Paakhi")
        names.add("Paavni")
        names.add("Padma")
        names.add("Pahal")
        names.add("Pallavi")
        names.add("Panini")
        names.add("Pari")
        names.add("Parul")
        names.add("Pihu")
        names.add("Pooja")
        names.add("Prachi")
        names.add("Prisha")
        names.add("Rabhya")
        names.add("Rachana")
        names.add("Ragini")
        names.add("Rajata")
        names.add("Ranhita")
        names.add("Rani")
        names.add("Ranya")
        names.add("Rati")
        names.add("Riddhi")
        names.add("Ritika")
        names.add("Rishika")
        names.add("Rohini")
        names.add("Roop")
        names.add("Roshni")
        names.add("Saachi")
        names.add("Sadhika")
        names.add("Sadhna")
        names.add("Sakshi")
        names.add("Samaira")
        names.add("Sandhya")
        names.add("Sanjana")
        names.add("Saumya")
        names.add("Shaila")
        names.add("Shalini")
        names.add("Sheetal")
        names.add("Shivanshika")
        names.add("Sonia")
        names.add("Suhana")
        names.add("Suhasini")
        names.add("Sweta")
        names.add("Tamanna")
        names.add("Tanika")
        names.add("Tanuja")
        names.add("Tanvi")
        names.add("Tara")
        names.add("Tejal")
        names.add("Tisya")
        names.add("Trayi")
        names.add("Triya")
        names.add("Turvi")
        names.add("Ubika")
        names.add("Uma")
        names.add("Urmi")
        names.add("Urvi")
        names.add("Vaani")
        names.add("Vaibhavi")
        names.add("Vamika")
        names.add("Vanya")
        names.add("Varsha")
        names.add("Vasaki")
        names.add("Vineeta")
        names.add("Vritika")
        names.add("Yamini")
        names.add("Yukta")
        names.add("Zoya")



        places.add("Anantapur, Andhra Pradesh")
        places.add("Chittoor, Andhra Pradesh")
        places.add("East Godavari, Andhra Pradesh")
        places.add("Guntur, Andhra Pradesh")
        places.add("Krishna, Andhra Pradesh")
        places.add("Kurnool, Andhra Pradesh")
        places.add("Nellore, Andhra Pradesh")
        places.add("Prakasam, Andhra Pradesh")
        places.add("Srikakulam, Andhra Pradesh")
        places.add("Visakhapatnam, Andhra Pradesh")
        places.add("Vizianagaram, Andhra Pradesh")
        places.add("West Godavari, Andhra Pradesh")
        places.add("YSR Kadapa, Andhra Pradesh")
        places.add("Tawang, Arunachal Pradesh")
        places.add("West Kameng, Arunachal Pradesh")
        places.add("East Kameng, Arunachal Pradesh")
        places.add("Papum Pare, Arunachal Pradesh")
        places.add("Kurung Kumey, Arunachal Pradesh")
        places.add("Kra Daadi, Arunachal Pradesh")
        places.add("Lower Subansiri, Arunachal Pradesh")
        places.add("Upper Subansiri, Arunachal Pradesh")
        places.add("West Siang, Arunachal Pradesh")
        places.add("East Siang, Arunachal Pradesh")
        places.add("Siang, Arunachal Pradesh")
        places.add("Upper Siang, Arunachal Pradesh")
        places.add("Lower Siang, Arunachal Pradesh")
        places.add("Lower Dibang Valley, Arunachal Pradesh")
        places.add("Dibang Valley, Arunachal Pradesh")
        places.add("Anjaw, Arunachal Pradesh")
        places.add("Lohit, Arunachal Pradesh")
        places.add("Namsai, Arunachal Pradesh")
        places.add("Changlang, Arunachal Pradesh")
        places.add("Tirap, Arunachal Pradesh")
        places.add("Longding, Arunachal Pradesh")
        places.add("Baksa, Assam")
        places.add("Barpeta, Assam")
        places.add("Biswanath, Assam")
        places.add("Bongaigaon, Assam")
        places.add("Cachar, Assam")
        places.add("Charaideo, Assam")
        places.add("Chirang, Assam")
        places.add("Darrang, Assam")
        places.add("Dhemaji, Assam")
        places.add("Dhubri, Assam")
        places.add("Dibrugarh, Assam")
        places.add("Goalpara, Assam")
        places.add("Golaghat, Assam")
        places.add("Hailakandi, Assam")
        places.add("Hojai, Assam")
        places.add("Jorhat, Assam")
        places.add("Kamrup Metropolitan, Assam")
        places.add("Kamrup, Assam")
        places.add("Karbi Anglong, Assam")
        places.add("Karimganj, Assam")
        places.add("Kokrajhar, Assam")
        places.add("Lakhimpur, Assam")
        places.add("Majuli, Assam")
        places.add("Morigaon, Assam")
        places.add("Nagaon, Assam")
        places.add("Nalbari, Assam")
        places.add("Dima Hasao, Assam")
        places.add("Sivasagar, Assam")
        places.add("Sonitpur, Assam")
        places.add("South Salmara-Mankachar, Assam")
        places.add("Tinsukia, Assam")
        places.add("Udalguri, Assam")
        places.add("West Karbi Anglong, Assam")
        places.add("Araria, Bihar")
        places.add("Arwal, Bihar")
        places.add("Aurangabad, Bihar")
        places.add("Banka, Bihar")
        places.add("Begusarai, Bihar")
        places.add("Bhagalpur, Bihar")
        places.add("Bhojpur, Bihar")
        places.add("Buxar, Bihar")
        places.add("Darbhanga, Bihar")
        places.add("East Champaran (Motihari), Bihar")
        places.add("Gaya, Bihar")
        places.add("Gopalganj, Bihar")
        places.add("Jamui, Bihar")
        places.add("Jehanabad, Bihar")
        places.add("Kaimur (Bhabua), Bihar")
        places.add("Katihar, Bihar")
        places.add("Khagaria, Bihar")
        places.add("Kishanganj, Bihar")
        places.add("Lakhisarai, Bihar")
        places.add("Madhepura, Bihar")
        places.add("Madhubani, Bihar")
        places.add("Munger (Monghyr), Bihar")
        places.add("Muzaffarpur, Bihar")
        places.add("Nalanda, Bihar")
        places.add("Nawada, Bihar")
        places.add("Patna, Bihar")
        places.add("Purnia (Purnea), Bihar")
        places.add("Rohtas, Bihar")
        places.add("Saharsa, Bihar")
        places.add("Samastipur, Bihar")
        places.add("Saran, Bihar")
        places.add("Sheikhpura, Bihar")
        places.add("Sheohar, Bihar")
        places.add("Sitamarhi, Bihar")
        places.add("Siwan, Bihar")
        places.add("Supaul, Bihar")
        places.add("Vaishali, Bihar")
        places.add("West Champaran, Bihar")
        places.add("Chandigarh, Chandigarh (UT)")
        places.add("Balod, Chhattisgarh")
        places.add("Baloda Bazar, Chhattisgarh")
        places.add("Balrampur, Chhattisgarh")
        places.add("Bastar, Chhattisgarh")
        places.add("Bemetara, Chhattisgarh")
        places.add("Bijapur, Chhattisgarh")
        places.add("Bilaspur, Chhattisgarh")
        places.add("Dantewada (South Bastar), Chhattisgarh")
        places.add("Dhamtari, Chhattisgarh")
        places.add("Durg, Chhattisgarh")
        places.add("Gariyaband, Chhattisgarh")
        places.add("Janjgir-Champa, Chhattisgarh")
        places.add("Jashpur, Chhattisgarh")
        places.add("Kabirdham (Kawardha), Chhattisgarh")
        places.add("Kanker (North Bastar), Chhattisgarh")
        places.add("Kondagaon, Chhattisgarh")
        places.add("Korba, Chhattisgarh")
        places.add("Korea, Chhattisgarh")
        places.add("Mahasamund, Chhattisgarh")
        places.add("Mungeli, Chhattisgarh")
        places.add("Narayanpur, Chhattisgarh")
        places.add("Raigarh, Chhattisgarh")
        places.add("Raipur, Chhattisgarh")
        places.add("Rajnandgaon, Chhattisgarh")
        places.add("Sukma, Chhattisgarh")
        places.add("Surajpur, Chhattisgarh")
        places.add("Surguja, Chhattisgarh")
        places.add("Dadra & Nagar Haveli, Dadra and Nagar Haveli (UT)")
        places.add("Daman, Daman and Diu (UT)")
        places.add("Diu, Daman and Diu (UT)")
        places.add("Central Delhi, Delhi (NCT)")
        places.add("East Delhi, Delhi (NCT)")
        places.add("New Delhi, Delhi (NCT)")
        places.add("North Delhi, Delhi (NCT)")
        places.add("North East  Delhi, Delhi (NCT)")
        places.add("North West  Delhi, Delhi (NCT)")
        places.add("Shahdara, Delhi (NCT)")
        places.add("South Delhi, Delhi (NCT)")
        places.add("South East Delhi, Delhi (NCT)")
        places.add("South West  Delhi, Delhi (NCT)")
        places.add("West Delhi, Delhi (NCT)")
        places.add("North Goa, Goa")
        places.add("South Goa, Goa")
        places.add("Ahmedabad, Gujarat")
        places.add("Amreli, Gujarat")
        places.add("Anand, Gujarat")
        places.add("Aravalli, Gujarat")
        places.add("Aravalli, Gujarat")
        places.add("Bharuch, Gujarat")
        places.add("Bhavnagar, Gujarat")
        places.add("Botad, Gujarat")
        places.add("Chhota Udepur, Gujarat")
        places.add("Dahod, Gujarat")
        places.add("Dangs (Ahwa), Gujarat")
        places.add("Devbhoomi Dwarka, Gujarat")
        places.add("Gandhinagar, Gujarat")
        places.add("Gir Somnath, Gujarat")
        places.add("Jamnagar, Gujarat")
        places.add("Junagadh, Gujarat")
        places.add("Kachchh, Gujarat")
        places.add("Kheda (Nadiad), Gujarat")
        places.add("Mahisagar, Gujarat")
        places.add("Mehsana, Gujarat")
        places.add("Morbi, Gujarat")
        places.add("Narmada (Rajpipla), Gujarat")
        places.add("Navsari, Gujarat")
        places.add("Panchmahal (Godhra), Gujarat")
        places.add("Patan, Gujarat")
        places.add("Porbandar, Gujarat")
        places.add("Rajkot, Gujarat")
        places.add("Sabarkantha (Himmatnagar), Gujarat")
        places.add("Surat, Gujarat")
        places.add("Surendranagar, Gujarat")
        places.add("Tapi (Vyara), Gujarat")
        places.add("Vadodara, Gujarat")
        places.add("Valsad, Gujarat")
        places.add("Ambala, Haryana")
        places.add("Bhiwani, Haryana")
        places.add("Charkhi Dadri, Haryana")
        places.add("Faridabad, Haryana")
        places.add("Fatehabad, Haryana")
        places.add("Gurgaon, Haryana")
        places.add("Hisar, Haryana")
        places.add("Jhajjar, Haryana")
        places.add("Jind, Haryana")
        places.add("Kaithal, Haryana")
        places.add("Karnal, Haryana")
        places.add("Kurukshetra, Haryana")
        places.add("Mahendragarh, Haryana")
        places.add("Mewat, Haryana")
        places.add("Palwal, Haryana")
        places.add("Panchkula, Haryana")
        places.add("Panipat, Haryana")
        places.add("Rewari, Haryana")
        places.add("Rohtak, Haryana")
        places.add("Sirsa, Haryana")
        places.add("Sonipat, Haryana")
        places.add("Yamunanagar, Haryana")
        places.add("Bilaspur, Himachal Pradesh")
        places.add("Chamba, Himachal Pradesh")
        places.add("Hamirpur, Himachal Pradesh")
        places.add("Kangra, Himachal Pradesh")
        places.add("Kinnaur, Himachal Pradesh")
        places.add("Kullu, Himachal Pradesh")
        places.add("Lahaul & Spiti, Himachal Pradesh")
        places.add("Mandi, Himachal Pradesh")
        places.add("Shimla, Himachal Pradesh")
        places.add("Sirmaur (Sirmour), Himachal Pradesh")
        places.add("Solan, Himachal Pradesh")
        places.add("Una, Himachal Pradesh")
        places.add("Anantnag, Jammu and Kashmir")
        places.add("Bandipore, Jammu and Kashmir")
        places.add("Baramulla, Jammu and Kashmir")
        places.add("Budgam, Jammu and Kashmir")
        places.add("Doda, Jammu and Kashmir")
        places.add("Ganderbal, Jammu and Kashmir")
        places.add("Jammu, Jammu and Kashmir")
        places.add("Kargil, Jammu and Kashmir")
        places.add("Kathua, Jammu and Kashmir")
        places.add("Kishtwar, Jammu and Kashmir")
        places.add("Kulgam, Jammu and Kashmir")
        places.add("Kupwara, Jammu and Kashmir")
        places.add("Leh, Jammu and Kashmir")
        places.add("Poonch, Jammu and Kashmir")
        places.add("Pulwama, Jammu and Kashmir")
        places.add("Rajouri, Jammu and Kashmir")
        places.add("Ramban, Jammu and Kashmir")
        places.add("Reasi, Jammu and Kashmir")
        places.add("Samba, Jammu and Kashmir")
        places.add("Shopian, Jammu and Kashmir")
        places.add("Srinagar, Jammu and Kashmir")
        places.add("Udhampur, Jammu and Kashmir")
        places.add("Bokaro, Jharkhand")
        places.add("Chatra, Jharkhand")
        places.add("Deoghar, Jharkhand")
        places.add("Dhanbad, Jharkhand")
        places.add("Dumka, Jharkhand")
        places.add("East Singhbhum, Jharkhand")
        places.add("Garhwa, Jharkhand")
        places.add("Giridih, Jharkhand")
        places.add("Godda, Jharkhand")
        places.add("Gumla, Jharkhand")
        places.add("Hazaribag, Jharkhand")
        places.add("Jamtara, Jharkhand")
        places.add("Khunti, Jharkhand")
        places.add("Koderma, Jharkhand")
        places.add("Latehar, Jharkhand")
        places.add("Lohardaga, Jharkhand")
        places.add("Pakur, Jharkhand")
        places.add("Palamu, Jharkhand")
        places.add("Ramgarh, Jharkhand")
        places.add("Ranchi, Jharkhand")
        places.add("Sahibganj, Jharkhand")
        places.add("Seraikela-Kharsawan, Jharkhand")
        places.add("Simdega, Jharkhand")
        places.add("West Singhbhum, Jharkhand")
        places.add("Bagalkot, Karnataka")
        places.add("Ballari (Bellary), Karnataka")
        places.add("Belagavi (Belgaum), Karnataka")
        places.add("Bengaluru (Bangalore) Rural, Karnataka")
        places.add("Bengaluru (Bangalore) Urban, Karnataka")
        places.add("Bidar, Karnataka")
        places.add("Chamarajanagar, Karnataka")
        places.add("Chikballapur, Karnataka")
        places.add("Chikkamagaluru (Chikmagalur), Karnataka")
        places.add("Chitradurga, Karnataka")
        places.add("Dakshina Kannada, Karnataka")
        places.add("Davangere, Karnataka")
        places.add("Dharwad, Karnataka")
        places.add("Gadag, Karnataka")
        places.add("Hassan, Karnataka")
        places.add("Haveri, Karnataka")
        places.add("Kalaburagi (Gulbarga), Karnataka")
        places.add("Kodagu, Karnataka")
        places.add("Kolar, Karnataka")
        places.add("Koppal, Karnataka")
        places.add("Mandya, Karnataka")
        places.add("Mysuru (Mysore), Karnataka")
        places.add("Raichur, Karnataka")
        places.add("Ramanagara, Karnataka")
        places.add("Shivamogga (Shimoga), Karnataka")
        places.add("Tumakuru (Tumkur), Karnataka")
        places.add("Udupi, Karnataka")
        places.add("Uttara Kannada (Karwar), Karnataka")
        places.add("Vijayapura (Bijapur), Karnataka")
        places.add("Yadgir, Karnataka")
        places.add("Alappuzha, Kerala")
        places.add("Ernakulam, Kerala")
        places.add("Idukki, Kerala")
        places.add("Kannur, Kerala")
        places.add("Kasaragod, Kerala")
        places.add("Kollam, Kerala")
        places.add("Kottayam, Kerala")
        places.add("Kozhikode, Kerala")
        places.add("Malappuram, Kerala")
        places.add("Palakkad, Kerala")
        places.add("Pathanamthitta, Kerala")
        places.add("Thiruvananthapuram, Kerala")
        places.add("Thrissur, Kerala")
        places.add("Wayanad, Kerala")
        places.add("Agatti, Lakshadweep (UT)")
        places.add("Amini, Lakshadweep (UT)")
        places.add("Androth, Lakshadweep (UT)")
        places.add("Bithra, Lakshadweep (UT)")
        places.add("Chethlath, Lakshadweep (UT)")
        places.add("Kavaratti, Lakshadweep (UT)")
        places.add("Kadmath, Lakshadweep (UT)")
        places.add("Kalpeni, Lakshadweep (UT)")
        places.add("Kilthan, Lakshadweep (UT)")
        places.add("Minicoy, Lakshadweep (UT)")
        places.add("Agar Malwa, Madhya Pradesh")
        places.add("Alirajpur, Madhya Pradesh")
        places.add("Anuppur, Madhya Pradesh")
        places.add("Ashoknagar, Madhya Pradesh")
        places.add("Balaghat, Madhya Pradesh")
        places.add("Barwani, Madhya Pradesh")
        places.add("Betul, Madhya Pradesh")
        places.add("Bhind, Madhya Pradesh")
        places.add("Bhopal, Madhya Pradesh")
        places.add("Burhanpur, Madhya Pradesh")
        places.add("Chhatarpur, Madhya Pradesh")
        places.add("Chhindwara, Madhya Pradesh")
        places.add("Damoh, Madhya Pradesh")
        places.add("Datia, Madhya Pradesh")
        places.add("Dewas, Madhya Pradesh")
        places.add("Dhar, Madhya Pradesh")
        places.add("Dindori, Madhya Pradesh")
        places.add("Guna, Madhya Pradesh")
        places.add("Gwalior, Madhya Pradesh")
        places.add("Harda, Madhya Pradesh")
        places.add("Hoshangabad, Madhya Pradesh")
        places.add("Indore, Madhya Pradesh")
        places.add("Jabalpur, Madhya Pradesh")
        places.add("Jhabua, Madhya Pradesh")
        places.add("Katni, Madhya Pradesh")
        places.add("Khandwa, Madhya Pradesh")
        places.add("Khargone, Madhya Pradesh")
        places.add("Mandla, Madhya Pradesh")
        places.add("Mandsaur, Madhya Pradesh")
        places.add("Morena, Madhya Pradesh")
        places.add("Narsinghpur, Madhya Pradesh")
        places.add("Neemuch, Madhya Pradesh")
        places.add("Panna, Madhya Pradesh")
        places.add("Raisen, Madhya Pradesh")
        places.add("Rajgarh, Madhya Pradesh")
        places.add("Ratlam, Madhya Pradesh")
        places.add("Rewa, Madhya Pradesh")
        places.add("Sagar, Madhya Pradesh")
        places.add("Satna, Madhya Pradesh")
        places.add("Sehore, Madhya Pradesh")
        places.add("Seoni, Madhya Pradesh")
        places.add("Shahdol, Madhya Pradesh")
        places.add("Shajapur, Madhya Pradesh")
        places.add("Sheopur, Madhya Pradesh")
        places.add("Shivpuri, Madhya Pradesh")
        places.add("Sidhi, Madhya Pradesh")
        places.add("Singrauli, Madhya Pradesh")
        places.add("Tikamgarh, Madhya Pradesh")
        places.add("Ujjain, Madhya Pradesh")
        places.add("Umaria, Madhya Pradesh")
        places.add("Vidisha, Madhya Pradesh")
        places.add("Ahmednagar, Maharashtra")
        places.add("Akola, Maharashtra")
        places.add("Amravati, Maharashtra")
        places.add("Aurangabad, Maharashtra")
        places.add("Beed, Maharashtra")
        places.add("Bhandara, Maharashtra")
        places.add("Buldhana, Maharashtra")
        places.add("Chandrapur, Maharashtra")
        places.add("Dhule, Maharashtra")
        places.add("Gadchiroli, Maharashtra")
        places.add("Gondia, Maharashtra")
        places.add("Hingoli, Maharashtra")
        places.add("Jalgaon, Maharashtra")
        places.add("Jalna, Maharashtra")
        places.add("Kolhapur, Maharashtra")
        places.add("Latur, Maharashtra")
        places.add("Mumbai City, Maharashtra")
        places.add("Mumbai Suburban, Maharashtra")
        places.add("Nagpur, Maharashtra")
        places.add("Nanded, Maharashtra")
        places.add("Nandurbar, Maharashtra")
        places.add("Nashik, Maharashtra")
        places.add("Osmanabad, Maharashtra")
        places.add("Palghar, Maharashtra")
        places.add("Parbhani, Maharashtra")
        places.add("Pune, Maharashtra")
        places.add("Raigad, Maharashtra")
        places.add("Ratnagiri, Maharashtra")
        places.add("Sangli, Maharashtra")
        places.add("Satara, Maharashtra")
        places.add("Sindhudurg, Maharashtra")
        places.add("Solapur, Maharashtra")
        places.add("Thane, Maharashtra")
        places.add("Wardha, Maharashtra")
        places.add("Washim, Maharashtra")
        places.add("Yavatmal, Maharashtra")
        places.add("Bishnupur, Manipur")
        places.add("Chandel, Manipur")
        places.add("Churachandpur, Manipur")
        places.add("Imphal East, Manipur")
        places.add("Imphal West, Manipur")
        places.add("Jiribam, Manipur")
        places.add("Kakching, Manipur")
        places.add("Kamjong, Manipur")
        places.add("Kangpokpi, Manipur")
        places.add("Noney, Manipur")
        places.add("Pherzawl, Manipur")
        places.add("Senapati, Manipur")
        places.add("Tamenglong, Manipur")
        places.add("Tengnoupal, Manipur")
        places.add("Thoubal, Manipur")
        places.add("Ukhrul, Manipur")
        places.add("East Garo Hills, Meghalaya")
        places.add("East Jaintia Hills, Meghalaya")
        places.add("East Khasi Hills, Meghalaya")
        places.add("North Garo Hills, Meghalaya")
        places.add("Ri Bhoi, Meghalaya")
        places.add("South Garo Hills, Meghalaya")
        places.add("South West Garo Hills, Meghalaya")
        places.add("South West Khasi Hills, Meghalaya")
        places.add("West Garo Hills, Meghalaya")
        places.add("West Jaintia Hills, Meghalaya")
        places.add("West Khasi Hills, Meghalaya")
        places.add("Aizawl, Mizoram")
        places.add("Champhai, Mizoram")
        places.add("Kolasib, Mizoram")
        places.add("Lawngtlai, Mizoram")
        places.add("Lunglei, Mizoram")
        places.add("Mamit, Mizoram")
        places.add("Saiha, Mizoram")
        places.add("Serchhip, Mizoram")
        places.add("Dimapur, Nagaland")
        places.add("Kiphire, Nagaland")
        places.add("Kohima, Nagaland")
        places.add("Longleng, Nagaland")
        places.add("Mokokchung, Nagaland")
        places.add("Mon, Nagaland")
        places.add("Peren, Nagaland")
        places.add("Phek, Nagaland")
        places.add("Tuensang, Nagaland")
        places.add("Wokha, Nagaland")
        places.add("Zunheboto, Nagaland")
        places.add("Angul, Odisha")
        places.add("Angul, Odisha")
        places.add("Balangir, Odisha")
        places.add("Balasore, Odisha")
        places.add("Bargarh, Odisha")
        places.add("Bhadrak, Odisha")
        places.add("Boudh, Odisha")
        places.add("Cuttack, Odisha")
        places.add("Deogarh, Odisha")
        places.add("Dhenkanal, Odisha")
        places.add("Gajapati, Odisha")
        places.add("Ganjam, Odisha")
        places.add("Jagatsinghapur, Odisha")
        places.add("Jagatsinghapur, Odisha")
        places.add("Jajpur, Odisha")
        places.add("Jharsuguda, Odisha")
        places.add("Kalahandi, Odisha")
        places.add("Kandhamal, Odisha")
        places.add("Kendrapara, Odisha")
        places.add("Kendujhar (Keonjhar), Odisha")
        places.add("Khordha, Odisha")
        places.add("Koraput, Odisha")
        places.add("Malkangiri, Odisha")
        places.add("Mayurbhanj, Odisha")
        places.add("Nabarangpur, Odisha")
        places.add("Nayagarh, Odisha")
        places.add("Nuapada, Odisha")
        places.add("Puri, Odisha")
        places.add("Rayagada, Odisha")
        places.add("Sambalpur, Odisha")
        places.add("Sonepur, Odisha")
        places.add("Sundargarh, Odisha")
        places.add("Karaikal, Puducherry (UT)")
        places.add("Mahe, Puducherry (UT)")
        places.add("Pondicherry, Puducherry (UT)")
        places.add("Yanam, Puducherry (UT)")
        places.add("Amritsar, Punjab")
        places.add("Barnala, Punjab")
        places.add("Bathinda, Punjab")
        places.add("Faridkot, Punjab")
        places.add("Fatehgarh Sahib, Punjab")
        places.add("Fazilka, Punjab")
        places.add("Ferozepur, Punjab")
        places.add("Gurdaspur, Punjab")
        places.add("Hoshiarpur, Punjab")
        places.add("Jalandhar, Punjab")
        places.add("Kapurthala, Punjab")
        places.add("Ludhiana, Punjab")
        places.add("Mansa, Punjab")
        places.add("Moga, Punjab")
        places.add("Muktsar, Punjab")
        places.add("Nawanshahr (Shahid Bhagat Singh Nagar), Punjab")
        places.add("Pathankot, Punjab")
        places.add("Patiala, Punjab")
        places.add("Rupnagar, Punjab")
        places.add("Sahibzada Ajit Singh Nagar (Mohali), Punjab")
        places.add("Sangrur, Punjab")
        places.add("Tarn Taran, Punjab")
        places.add("Ajmer, Rajasthan")
        places.add("Alwar, Rajasthan")
        places.add("Banswara, Rajasthan")
        places.add("Baran, Rajasthan")
        places.add("Barmer, Rajasthan")
        places.add("Bharatpur, Rajasthan")
        places.add("Bhilwara, Rajasthan")
        places.add("Bikaner, Rajasthan")
        places.add("Bundi, Rajasthan")
        places.add("Chittorgarh, Rajasthan")
        places.add("Churu, Rajasthan")
        places.add("Dausa, Rajasthan")
        places.add("Dholpur, Rajasthan")
        places.add("Dungarpur, Rajasthan")
        places.add("Hanumangarh, Rajasthan")
        places.add("Jaipur, Rajasthan")
        places.add("Jaisalmer, Rajasthan")
        places.add("Jalore, Rajasthan")
        places.add("Jhalawar, Rajasthan")
        places.add("Jhunjhunu, Rajasthan")
        places.add("Jodhpur, Rajasthan")
        places.add("Karauli, Rajasthan")
        places.add("Kota, Rajasthan")
        places.add("Nagaur, Rajasthan")
        places.add("Pali, Rajasthan")
        places.add("Pratapgarh, Rajasthan")
        places.add("Rajsamand, Rajasthan")
        places.add("Sawai Madhopur, Rajasthan")
        places.add("Sika, Rajasthan")
        places.add("Sirohi, Rajasthan")
        places.add("Sri Ganganagar, Rajasthan")
        places.add("Tonk, Rajasthan")
        places.add("Udaipur, Rajasthan")
        places.add("East Sikkim, Sikkim")
        places.add("North Sikkim, Sikkim")
        places.add("South Sikkim, Sikkim")
        places.add("West Sikkim, Sikkim")
        places.add("Ariyalur, Tamil Nadu")
        places.add("Chenna, Tamil Nadu")
        places.add("Coimbatore, Tamil Nadu")
        places.add("Cuddalore, Tamil Nadu")
        places.add("Dharmapuri, Tamil Nadu")
        places.add("Dindigul, Tamil Nadu")
        places.add("Erode, Tamil Nadu")
        places.add("Kanchipuram, Tamil Nadu")
        places.add("Kanyakumari, Tamil Nadu")
        places.add("Karur, Tamil Nadu")
        places.add("Krishnagiri, Tamil Nadu")
        places.add("Madurai, Tamil Nadu")
        places.add("Nagapattinam, Tamil Nadu")
        places.add("Namakkal, Tamil Nadu")
        places.add("Nilgiris, Tamil Nadu")
        places.add("Perambalur, Tamil Nadu")
        places.add("Pudukkottai, Tamil Nadu")
        places.add("Ramanathapuram, Tamil Nadu")
        places.add("Salem, Tamil Nadu")
        places.add("Sivaganga, Tamil Nadu")
        places.add("Thanjavur, Tamil Nadu")
        places.add("Theni, Tamil Nadu")
        places.add("Thoothukudi (Tuticorin), Tamil Nadu")
        places.add("Tiruchirappalli, Tamil Nadu")
        places.add("Tirunelveli, Tamil Nadu")
        places.add("Tiruppur, Tamil Nadu")
        places.add("Tiruvallur, Tamil Nadu")
        places.add("Tiruvannamalai, Tamil Nadu")
        places.add("Tiruvarur, Tamil Nadu")
        places.add("Vellore, Tamil Nadu")
        places.add("Viluppuram, Tamil Nadu")
        places.add("Virudhunagar, Tamil Nadu")
        places.add("Adilabad, Telangana")
        places.add("Bhadradri Kothagudem, Telangana")
        places.add("Hyderabad, Telangana")
        places.add("Jagtial, Telangana")
        places.add("Jangaon, Telangana")
        places.add("Jayashankar Bhoopalpally, Telangana")
        places.add("Jogulamba Gadwal, Telangana")
        places.add("Kamareddy, Telangana")
        places.add("Karimnagar, Telangana")
        places.add("Khammam, Telangana")
        places.add("Komaram Bheem Asifabad, Telangana")
        places.add("Mahabubabad, Telangana")
        places.add("Mahabubnagar, Telangana")
        places.add("Mancherial, Telangana")
        places.add("Medak, Telangana")
        places.add("Medchal, Telangana")
        places.add("Nagarkurnool, Telangana")
        places.add("Nalgonda, Telangana")
        places.add("Nirmal, Telangana")
        places.add("Nizamabad, Telangana")
        places.add("Peddapalli, Telangana")
        places.add("Rajanna Sircilla, Telangana")
        places.add("Rangareddy, Telangana")
        places.add("Sangareddy, Telangana")
        places.add("Siddipet, Telangana")
        places.add("Suryapet, Telangana")
        places.add("Vikarabad, Telangana")
        places.add("Wanaparthy, Telangana")
        places.add("Warangal (Rural), Telangana")
        places.add("Warangal (Urban), Telangana")
        places.add("Yadadri Bhuvanagiri, Telangana")
        places.add("Dhalai, Tripura")
        places.add("Gomati, Tripura")
        places.add("Khowai, Tripura")
        places.add("North Tripura, Tripura")
        places.add("Sepahijala, Tripura")
        places.add("South Tripura, Tripura")
        places.add("Unakoti, Tripura")
        places.add("West Tripura, Tripura")
        places.add("Almora, Uttarakhand")
        places.add("Bageshwar, Uttarakhand")
        places.add("Chamoli, Uttarakhand")
        places.add("Champawat, Uttarakhand")
        places.add("Dehradun, Uttarakhand")
        places.add("Haridwar, Uttarakhand")
        places.add("Nainital, Uttarakhand")
        places.add("Pauri Garhwal, Uttarakhand")
        places.add("Pithoragarh, Uttarakhand")
        places.add("Rudraprayag, Uttarakhand")
        places.add("Tehri Garhwal, Uttarakhand")
        places.add("Udham Singh Nagar, Uttarakhand")
        places.add("Uttarkashi, Uttarakhand")
        places.add("Agra, Uttar Pradesh")
        places.add("Aligarh, Uttar Pradesh")
        places.add("Allahabad, Uttar Pradesh")
        places.add("Ambedkar Nagar, Uttar Pradesh")
        places.add("Amethi (Chatrapati Sahuji Mahraj Nagar), Uttar Pradesh")
        places.add("Amroha (J.P. Nagar), Uttar Pradesh")
        places.add("Auraiya, Uttar Pradesh")
        places.add("Azamgarh, Uttar Pradesh")
        places.add("Baghpat, Uttar Pradesh")
        places.add("Bahraich, Uttar Pradesh")
        places.add("Ballia, Uttar Pradesh")
        places.add("Balrampur, Uttar Pradesh")
        places.add("Banda, Uttar Pradesh")
        places.add("Barabanki, Uttar Pradesh")
        places.add("Bareilly, Uttar Pradesh")
        places.add("Basti, Uttar Pradesh")
        places.add("Bhadohi, Uttar Pradesh")
        places.add("Bijnor, Uttar Pradesh")
        places.add("Budaun, Uttar Pradesh")
        places.add("Bulandshahr, Uttar Pradesh")
        places.add("Chandauli, Uttar Pradesh")
        places.add("Chitrakoot, Uttar Pradesh")
        places.add("Deoria, Uttar Pradesh")
        places.add("Etah, Uttar Pradesh")
        places.add("Etawah, Uttar Pradesh")
        places.add("Faizabad, Uttar Pradesh")
        places.add("Farrukhabad, Uttar Pradesh")
        places.add("Fatehpur, Uttar Pradesh")
        places.add("Firozabad, Uttar Pradesh")
        places.add("Gautam Buddha Nagar, Uttar Pradesh")
        places.add("Ghaziabad, Uttar Pradesh")
        places.add("Ghazipur, Uttar Pradesh")
        places.add("Gonda, Uttar Pradesh")
        places.add("Gorakhpur, Uttar Pradesh")
        places.add("Hamirpur, Uttar Pradesh")
        places.add("Hapur (Panchsheel Nagar), Uttar Pradesh")
        places.add("Hardoi, Uttar Pradesh")
        places.add("Hathras, Uttar Pradesh")
        places.add("Jalaun, Uttar Pradesh")
        places.add("Jaunpur, Uttar Pradesh")
        places.add("Jhansi, Uttar Pradesh")
        places.add("Kannauj, Uttar Pradesh")
        places.add("Kanpur Dehat, Uttar Pradesh")
        places.add("Kanpur Nagar, Uttar Pradesh")
        places.add("Kanshiram Nagar (Kasganj), Uttar Pradesh")
        places.add("Kaushambi, Uttar Pradesh")
        places.add("Kushinagar (Padrauna), Uttar Pradesh")
        places.add("Lakhimpur - Kheri, Uttar Pradesh")
        places.add("Lalitpur, Uttar Pradesh")
        places.add("Lucknow, Uttar Pradesh")
        places.add("Maharajganj, Uttar Pradesh")
        places.add("Mahoba, Uttar Pradesh")
        places.add("Mainpuri, Uttar Pradesh")
        places.add("Mathura, Uttar Pradesh")
        places.add("Mau, Uttar Pradesh")
        places.add("Meerut, Uttar Pradesh")
        places.add("Mirzapur, Uttar Pradesh")
        places.add("Moradabad, Uttar Pradesh")
        places.add("Muzaffarnagar, Uttar Pradesh")
        places.add("Pilibhit, Uttar Pradesh")
        places.add("Pratapgarh, Uttar Pradesh")
        places.add("RaeBareli, Uttar Pradesh")
        places.add("Rampur, Uttar Pradesh")
        places.add("Saharanpur, Uttar Pradesh")
        places.add("Sambhal (Bhim Nagar), Uttar Pradesh")
        places.add("Sant Kabir Nagar, Uttar Pradesh")
        places.add("Shahjahanpur, Uttar Pradesh")
        places.add("Shamali (Prabuddh Nagar), Uttar Pradesh")
        places.add("Shravasti, Uttar Pradesh")
        places.add("Siddharth Nagar, Uttar Pradesh")
        places.add("Sitapur, Uttar Pradesh")
        places.add("Sonbhadra, Uttar Pradesh")
        places.add("Sultanpur, Uttar Pradesh")
        places.add("Unnao, Uttar Pradesh")
        places.add("Varanasi, Uttar Pradesh")
        places.add("Alipurduar, West Bengal")
        places.add("Bankura, West Bengal")
        places.add("Birbhum, West Bengal")
        places.add("Burdwan (Bardhaman), West Bengal")
        places.add("Cooch Behar, West Bengal")
        places.add("Dakshin Dinajpur (South Dinajpur), West Bengal")
        places.add("Darjeeling, West Bengal")
        places.add("Hooghly, West Bengal")
        places.add("Howrah, West Bengal")
        places.add("Jalpaiguri, West Bengal")
        places.add("Kalimpong, West Bengal")
        places.add("Kolkata, West Bengal")
        places.add("Malda, West Bengal")
        places.add("Murshidabad, West Bengal")
        places.add("Nadia, West Bengal")
        places.add("North 24 Parganas, West Bengal")
        places.add("Paschim Medinipur (West Medinipur), West Bengal")
        places.add("Purba Medinipur (East Medinipur), West Bengal")
        places.add("Purulia, West Bengal")
        places.add("South 24 Parganas, West Bengal")
        places.add("Uttar Dinajpur (North Dinajpur), West Bengal")


    }

    def getName(){
        if(names.size()==0) populateNamesAndPlaces()
        Random random = new Random()

        int x = random.nextInt(names.size())
        return names.get(x)
    }

    def getPlace(){
        if(places.size()==0) populateNamesAndPlaces()
        Random random = new Random()

        int x = random.nextInt(places.size())
        return places.get(x)
    }

    def createPrepjoyUsers(siteId){
        if(names.size()==0) populateNamesAndPlaces()
        if(places.size()==0) populateNamesAndPlaces()

        //loop through the names first
        User user
        Random random = new Random()
        String placesTemp
        WinGenerator winGenerator

        boolean addToInstitute = false
        KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("demoInstituteId",siteId)
        Integer defaultBatchId
        if(keyValueMst!=null){
            addToInstitute = true
            defaultBatchId = (CourseBatchesDtl.findByConductedByAndName(new Integer(keyValueMst.keyValue), "Default")).id
        }

        for(int i=0;i<names.size();i++){

            try {
                winGenerator = new WinGenerator()
                winGenerator.save(failOnError: true)
                placesTemp = places.get(random.nextInt(places.size()))
                user = new User(username: siteId + "_" + names[i].toLowerCase(), name: names[i], password: names[i].toLowerCase(), win: winGenerator.id, siteId: siteId, userType: "dummy",
                        city: placesTemp.split(',')[0], state: placesTemp.split(',')[1])
                user.save(failOnError: true, flush: true)

                if (addToInstitute) {
                    instituteService.addUserToBatch(user.username, defaultBatchId, null, null, null, siteId)
                }
            }catch(Exception e){
                println("exception for the user "+names[i])
            }
        }
    }

    def setFirstTenPlays(siteId)
    {
        User user
        KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("demoInstituteId",siteId)
        String instituteId = null
        if(keyValueMst!=null){
            instituteId = keyValueMst.keyValue

        }
        if(names.size()==0) populateNamesAndPlaces()
        Random random = new Random()
        QuizRecMst quizRecMst
        int x
        for(int i=0;i<10;i++) {
            x = random.nextInt(names.size())
            user = User.findByUsernameAndUserType(siteId + "_" + names.get(x).toLowerCase(),"dummy")
            if (user != null) {
                x = random.nextInt(100)
                quizRecMst = new QuizRecMst(resId: new Integer(-1), username: user.username, points: new Integer(x),
                        matchStatus: "win", challenger: "challenger", challengerPoints: new Integer(5), challengerTime: new Double(46),
                        userTime: new Double(x + 10), siteId: user.siteId, source: "dummy",
                        quizId: null, quizType: "regular",
                        noOfQuestions: new Integer(x), status: "dummy",instituteId:instituteId!=null?new Integer(instituteId):null
                )
                quizRecMst.save(failOnError: true, flush: true)
            }
        }

        String todaysDate =  utilService.getTodaysDate()


        if(instituteId!=null) {
            getDailyLeaderBoard(todaysDate, new Integer(siteId), instituteId)
            getWeeklyLeaderBoard(todaysDate, new Integer(siteId), instituteId)
            getMonthlyLeaderBoard(todaysDate, new Integer(siteId), instituteId)
        }

        //reset the main ranks also
        getDailyLeaderBoard(todaysDate, new Integer(siteId), null)
        getWeeklyLeaderBoard(todaysDate, new Integer(siteId), null)
        getMonthlyLeaderBoard(todaysDate, new Integer(siteId), null)

    }

    def updateQuizResults(request){

        def jsonObject = request.JSON
        JSONArray userAnswers = jsonObject.userAnswers;
        String username = null
        String instituteId = null
        if(jsonObject.instituteId!=null) instituteId = jsonObject.instituteId
        String subject = null
        HashMap returnValues = new HashMap()
        boolean bookTagsFound = false
        String level,syllabus,grade
        if(jsonObject.chapterId!=null&&!"".equals(jsonObject.chapterId)){
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Integer(jsonObject.chapterId))
            if(chaptersMst!=null){
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
                if(booksTagDtl!=null) {
                    level = booksTagDtl.level
                    syllabus = booksTagDtl.syllabus
                    grade = booksTagDtl.grade
                    subject = booksTagDtl.subject
                    bookTagsFound = true
                }
            }
        }
        else if(jsonObject.dailyTestDtlId!=null){
            DailyTestsMst dailyTestsMst = getDailyTestMst(new Integer(jsonObject.dailyTestDtlId))
            level = dailyTestsMst.level
            syllabus = dailyTestsMst.syllabus
            grade = dailyTestsMst.grade
            subject = dailyTestsMst.testSubject
            bookTagsFound = true
        }

        if(!bookTagsFound){
            ObjectiveMst objectiveMst = logsService.getObjectiveMst(userAnswers[0].id)
            if(objectiveMst!=null) {
                ResourceDtl resourceDtl = logsService.getResourceDtl(objectiveMst.quizId)
                if (resourceDtl != null) {
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                    if (chaptersMst != null) {
                        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
                        if(booksTagDtl!=null) {
                            level = booksTagDtl.level
                            syllabus = booksTagDtl.syllabus
                            grade = booksTagDtl.grade
                            subject = booksTagDtl.subject
                            bookTagsFound = true
                        }
                    }
                }
            }
        }
       if(bookTagsFound){
           returnValues.put("level",level)
           returnValues.put("syllabus",syllabus)
           returnValues.put("grade",grade)
           returnValues.put("subject",subject)
       }

       if(springSecurityService.currentUser!=null) {
            username = springSecurityService.currentUser.username

            //last option
            if(instituteId==null) {
                User user = dataProviderService.getUserMst(username)
                if(user!=null&&user.signedUpInstituteId!=null) instituteId = ""+user.signedUpInstituteId
            }

        }
        String objIds=""
        for(int i=0;i<userAnswers.size();i++) {
            objIds += userAnswers[i].id+","
        }
        objIds = objIds.substring(0,objIds.length()-1)
        List quizStatisticsList = QuizStatistics.findAllByObjIdInList(Arrays.asList(objIds.split(",")))
        returnValues.put("quizStatisticsList",quizStatisticsList)
        if(springSecurityService.currentUser!=null) {
            QuizRecMst quizRecMst = new QuizRecMst(resId: jsonObject.resId != null && !"".equals(jsonObject.resId) ? new Integer(jsonObject.resId) : null, username: username, points: new Integer(jsonObject.userPoints),
                    matchStatus: jsonObject.matchStatus, challenger: jsonObject.botName, challengerPoints: new Integer(jsonObject.botPoints), challengerTime: new Double(jsonObject.botTime),
                    userTime: new Double(jsonObject.userTime), siteId: new Integer(jsonObject.siteId), source: jsonObject.source,
                    quizId: jsonObject.quizId != null ? new Integer(jsonObject.quizId) : null, language: jsonObject.language, quizType: jsonObject.quizType,
                    noOfQuestions: jsonObject.noOfQuestions != null ? new Integer(jsonObject.noOfQuestions) : null, dailyTestDtlId: jsonObject.dailyTestDtlId != null ? new Integer(jsonObject.dailyTestDtlId) : null,
                    realDailyTestDtlId: jsonObject.realDailyTestDtlId != null ? new Integer(jsonObject.realDailyTestDtlId) : null,
                    currentAffairsType: jsonObject.currentAffairsType != null ? "" + jsonObject.currentAffairsType : null,
                    userChallengeId: jsonObject.userChallengeId != null ? new Integer(jsonObject.userChallengeId) : null, status: jsonObject.status,
                    testGenId: jsonObject.testGenId != null ? new Integer(jsonObject.testGenId) : null, instituteId: instituteId != null ? new Integer(instituteId) : null,
                    parent_quiz_rec_id: jsonObject.parentQuizRecId, retestType: jsonObject.questionOptions)
            quizRecMst.save(failOnError: true, flush: true)
            asyncLogsService.updateQuizRecDtl(quizRecMst.id, jsonObject.chapterId != null && !"".equals(jsonObject.chapterId) ? new Integer(jsonObject.chapterId) : null, userAnswers, username, quizRecMst.siteId, subject)
            returnValues.put("quizRecId", quizRecMst.id)

            if(jsonObject.testGenId == null) {
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(jsonObject.resId))
                if (jsonObject.resId != null && !"".equals(jsonObject.resId) && jsonObject.score != null && !"".equals(jsonObject.score)) {
                    quizRecMst.userScore = new Double(jsonObject.score)
                    boolean showResults = true
                    println("liveMockMstId"+jsonObject.liveMockMstId)
                    println(jsonObject.liveMockMstId && !"".equals(jsonObject.liveMockMstId))

                    if(jsonObject.liveMockMstId && !"".equals(jsonObject.liveMockMstId)) {
                        println("liveMockMstId"+jsonObject.liveMockMstId)
                        LiveMockMst liveMockMst = LiveMockMst.findById(new Integer(jsonObject.liveMockMstId))
                        println("testEndDate"+liveMockMst.testEndDate)

                        if(liveMockMst!=null && (liveMockMst.isResultDeclared == "false" || liveMockMst.isResultDeclared == null)) {
                            println("came now")
                            quizRecMst.liveMockMstId = new Long(jsonObject.liveMockMstId)
                            returnValues.put("liveMockMstId", jsonObject.liveMockMstId)
                            returnValues.put("testEndDate", liveMockMst.testEndDate)
                            returnValues.put("isResultDeclared", liveMockMst.isResultDeclared)
                            returnValues.put("testResultDate", resourceDtl.testResultDate)
                            showResults = false
                        }
                    }
                    quizRecMst.save(failOnError: true, flush: true)

                    if(!jsonObject.liveMockMstId || "".equals(jsonObject.liveMockMstId)){
                        addRank(jsonObject)
                    }

                    returnValues.put("showResults", showResults)
                }

                returnValues.put("quizName",resourceDtl?.resourceName)

                //leader board logic
                if("71".equals(""+jsonObject.siteId)) {
                    String todaysDate = utilService.getTodaysDate()
                    if (redisService.("prepJoyTodaysRank_" + jsonObject.siteId + "_" + todaysDate) == null || "No Ranks".equals(redisService.("prepJoyTodaysRank_" + jsonObject.siteId + "_" + todaysDate)) || Integer.parseInt(redisService.("prepJoyTodaysRank_noOfRanks_" + jsonObject.siteId + "_" + todaysDate)) < 10) getDailyLeaderBoard(todaysDate, new Integer(jsonObject.siteId), null)
                    else {
                        // to see if the ranks needs to be recalculated
                        int usersDailyTotal = 0
                        int lowestRankTotal = 0
                        if (usersDailyTotal > lowestRankTotal) getDailyLeaderBoard(todaysDate, new Integer(jsonObject.siteId), null)
                        returnValues.put("newRanks", redisService.("prepJoyTodaysRank_" + jsonObject.siteId + "_" + todaysDate))
                    }
                }

                //if the quiz was taken from the saved quiz, then delete the old records
                if (jsonObject.quizRecId != null && !"null".equals(jsonObject.quizRecId && !"".equals(jsonObject.quizRecId))) {
                    QuizRecDtl.executeUpdate("delete QuizRecDtl where quizRecId=" + jsonObject.quizRecId)
                    QuizRecMst.executeUpdate("delete QuizRecMst where id=" + jsonObject.quizRecId)
                }
            }else{
                println("**** testGenId is not null ****")
                returnValues.put("testGenId", jsonObject.testGenId)
            }
        }
        return returnValues

    }



    def addRank(requestData) {
        String username = requestData.userName
        //want to first check if requestData.score is null or not and if not just return without doing anything as we dont want to add a rank for a null score
        if(requestData.userPoints==null||"".equals(requestData.userPoints)||"null".equals(requestData.userPoints)) return
        else {
            Double newScore = new Double("" + requestData.score)
            Integer resId = new Integer("" + requestData.resId)

            // Fetch the current rankings for the given quiz
            List<QuizRanks> currentRanks = QuizRanks.findAllByResId(resId, [sort: 'score', order: 'desc'])

            // Check if the user already has a rank in the current list
            QuizRanks existingRank = currentRanks.find { it.username == username }

            if (existingRank) {
                // If the new score is less than or equal to the existing score, ignore the new score
                if (newScore <= existingRank.score) {
                    return
                } else {
                    // Remove the existing rank as we will be updating it
                    currentRanks.remove(existingRank)
                    existingRank.delete()
                }
            }

            // Add the new score to the list and sort it
            currentRanks.add(new QuizRanks(username: username, score: newScore, resId: resId, dateCreated: new Date(), userTime: new Double(requestData.userTime)))
            currentRanks = currentRanks.sort { -it.score }  // Sort in descending order by score

            // If the list exceeds 10 items, remove the lowest scores
            if (currentRanks.size() > 10) {
                currentRanks = currentRanks.take(10)
            }

            // Update the ranks in the list
            currentRanks.eachWithIndex { rank, index ->
                rank.userRank = index + 1
                rank.save(flush: true)
            }
        }
    }

    def calculateRanksForLiveMockTest(Integer resId, Integer liveMockMstId) {
        try {
            // Query all records from QuizRecMst where resId matches and fromLiveMockTest = "true"
            List<QuizRecMst> liveMockRecords = QuizRecMst.findAllByResIdAndLiveMockMstId(resId, liveMockMstId, [sort: 'userScore', order: 'desc'])

            if (!liveMockRecords || liveMockRecords.isEmpty()) {
                println("No live mock test records found for resId: ${resId}")
                return
            }

            // Clear existing ranks for this resId to recalculate fresh
            QuizRanks.findAllByResId(resId).each { it.delete(flush: true) }

            // Create a list to hold all quiz ranks
            List<QuizRanks> allRanks = []

            // Process each quiz record and create QuizRanks entries
            liveMockRecords.each { quizRec ->
                // Skip records with null or invalid scores
                if (quizRec.userScore == null) {
                    return // continue to next iteration
                }

                // Create QuizRanks entry for this record
                QuizRanks quizRank = new QuizRanks(
                        username: quizRec.username,
                        score: quizRec.userScore,
                        resId: resId,
                        dateCreated: quizRec.dateCreated ?: new Date(),
                        userTime: quizRec.userTime,
                        liveMockMstId: liveMockMstId
                )
                allRanks.add(quizRank)
            }

            // Sort all ranks by score in descending order
            allRanks = allRanks.sort { -it.score }

            // Assign ranks and save to database
            allRanks.eachWithIndex { rank, index ->
                rank.userRank = index + 1
                rank.save(flush: true)
            }

            // Update LiveMockMst with totalAttempts and isResultDeclared
            LiveMockMst liveMockMst = LiveMockMst.findById(liveMockMstId)
            liveMockMst.totalAttempts = liveMockRecords.size().toString()
            liveMockMst.isResultDeclared = "true"
            liveMockMst.save(failOnError: true, flush: true)
            println("Successfully calculated and saved ranks for ${allRanks.size()} records for resId: ${resId}")
        }catch (Exception e) {
            println("Error calculating ranks for live mock test: ${e.message}")
        }
    }



    def getUserPrepJoyDetails(String username) {

        UserPointsPrepJoy userPointsPrepJoy = getUserPoints(username)

        def sql = "select count(medal) medalcount,medal from user_medals where username='" + username + "' group by medal"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        String userPrepJoyDetails = ""
        if (userPointsPrepJoy != null) {
            userPrepJoyDetails = "totalPoints:" + userPointsPrepJoy.totalPoints + ",currentBadge:" + userPointsPrepJoy.currentBadge
            int totalMedals = 0

            for (int i = 0; i < results.size(); i++) {
                userPrepJoyDetails += "," + results[i].medal + ":" + results[i].medalCount
                totalMedals += Integer.parseInt(""+results[i].medalcount)
            }
            userPrepJoyDetails += ",totalMedals:" + totalMedals
        }else{
            userPrepJoyDetails = "totalPoints:10,currentBadge:pHunter,totalMedals:0"
        }
        String todaysDate =  utilService.getTodaysDate()
        if(redisService.("prepJoyTotalPoints_"+username+"_"+todaysDate)==null) getUsersTodaysTotalPoints(username,todaysDate,null)
        userPrepJoyDetails +=",todaysPoints:"+redisService.("prepJoyTotalPoints_"+username+"_"+todaysDate)
        redisService.("userPrepjoydetails_"+username) = userPrepJoyDetails
    }

    def getUserPoints(String username){
        UserPointsPrepJoy userPointsPrepJoy  = redisService.memoizeDomainObject(UserPointsPrepJoy, "prepJoyUser_"+username) {
            return UserPointsPrepJoy.findByUsername(username)
        }
        return userPointsPrepJoy
    }

    def getUsersTodaysTotalPoints(String username,todaysDate,instituteId){
        String institutePrefix=""
        if(instituteId!=null) institutePrefix="institute_"+instituteId+"_"

        def sql = "SELECT sum(points) totalPoints FROM wsuser.quiz_rec_mst where date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) = date('"+todaysDate+"') and username='" + username + "'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        String totalPoints="0"
        if(results[0].totalPoints!=null) totalPoints = ""+results[0].totalPoints

        redisService.(institutePrefix+"prepJoyTotalPoints_"+username+"_"+todaysDate)=totalPoints
    }

    def getUsersWeeklyTotalPoints(String username,todaysDate,instituteId){
        String institutePrefix=""
        if(instituteId!=null) institutePrefix="institute_"+instituteId+"_"

        def sql = "SELECT sum(points) totalPoints FROM wsuser.quiz_rec_mst where " +
                "                date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+todaysDate+"','%d-%m-%Y')" +
                "               and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >  DATE_SUB(STR_TO_DATE('"+todaysDate+"','%d-%m-%Y'), INTERVAL 7 DAY)"
                " and username='" + username + "'"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        String totalPoints="0"
        if(results[0].totalPoints!=null) totalPoints = ""+results[0].totalPoints

        redisService.(institutePrefix+"prepJoyWeeklyTotalPoints_"+username+"_"+todaysDate)=totalPoints
    }

    def getUsersMonthlyTotalPoints(String username,todaysDate,instituteId){
        String institutePrefix=""
        if(instituteId!=null) institutePrefix="institute_"+instituteId+"_"

        def sql = "SELECT sum(points) totalPoints FROM wsuser.quiz_rec_mst where " +
                "                date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+todaysDate+"','%d-%m-%Y')" +
                "               and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >  DATE_SUB(STR_TO_DATE('"+todaysDate+"','%d-%m-%Y'), INTERVAL 30 DAY)"
        " and username='" + username + "'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        String totalPoints="0"
        if(results[0].totalPoints!=null) totalPoints = ""+results[0].totalPoints

        redisService.(institutePrefix+"prepJoyMonthlyTotalPoints_"+username+"_"+todaysDate)=totalPoints
    }

    def getDailyLeaderBoard(todaysDate,Integer siteId,String instituteId){

        String institutePrefix=""
        def sql = "SELECT sum(qrm.points) userPoints, u.name,u.username,u.profilepic,u.id,COALESCE(qrm.institute_id,null) institute_id FROM wsuser.quiz_rec_mst qrm,user u where date(DATE_ADD(qrm.date_created, INTERVAL '5:30' HOUR_MINUTE)) = date('"+todaysDate+"') " +
                " and qrm.site_id="+siteId;

        if(instituteId!=null) {
            sql+= " and qrm.institute_id="+instituteId
            institutePrefix="institute_"+instituteId+"_"
        }


             sql+=   " and u.username=qrm.username group by u.username,u.name,u.profilepic,u.id,institute_id   order by userPoints desc limit 10"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        int i=0
        int numberOfRanks = results.size()
        int lowestPoints = 0;
        String state
        int defaultInstituteId= -1
        KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("demoInstituteId",siteId)
        if(keyValueMst!=null){
            defaultInstituteId = Integer.parseInt(keyValueMst.keyValue)
        }
        List rankUsers = results.collect { rank ->
            User user = dataProviderService.getUserMst(rank.username)
            UserPointsPrepJoy userPoints = UserPointsPrepJoy.findByUsernameAndPointsTypeAndSiteId(rank.userName,"prepjoy",siteId)

            i++;
            lowestPoints=0;

            lowestPoints = Integer.parseInt(""+rank.userPoints).intValue()
            state = user.state
            if(rank.institute_id!=null&&instituteId==null&&rank.institute_id.intValue()!=defaultInstituteId) {
                state = dataProviderService.getInstituteMst(rank.institute_id).name
            }
            return [rank:i, name:rank.name, username:utilService.userManagementService.encrypt(rank.username),
                    userPoints: rank.userPoints,profilePic:rank.profilepic, userId:rank.id,
                    state:state,memberSince:convertDate(user.dateCreated,"UTC","IST"),
                    currentBadge: userPoints!=null?userPoints.currentBadge:null]
        }

        if(results.size()>0) {
            Gson gson = new Gson();
            String element = gson.toJson(rankUsers, new TypeToken<List>() {}.getType())
            redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + todaysDate) = element
        }else{
            redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + todaysDate) = "No Ranks"
        }

        redisService.(institutePrefix+"prepJoyTodaysRank_noOfRanks_"+siteId+"_"+todaysDate) = ""+numberOfRanks
        redisService.(institutePrefix+"prepJoyTodaysRank_lowestPoints_"+siteId+"_"+todaysDate) = ""+lowestPoints

    }

    def getWeeklyLeaderBoard(todaysDate,Integer siteId,String instituteId){
        String institutePrefix=""
        def sql = "SELECT sum(qrm.points) userPoints, u.name,u.username,u.profilepic,u.id,COALESCE(qrm.institute_id,null) institute_id FROM wsuser.quiz_rec_mst qrm,user u where " +
                "                date(DATE_ADD(qrm.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+todaysDate+"','%Y-%m-%d')" +
                "               and date(DATE_ADD(qrm.date_created, INTERVAL '5:30' HOUR_MINUTE)) >  DATE_SUB(STR_TO_DATE('"+todaysDate+"','%Y-%m-%d'), INTERVAL 7 DAY)"+
                " and qrm.site_id="+siteId;


        if(instituteId!=null) {
            sql+= " and qrm.institute_id="+instituteId
            institutePrefix="institute_"+instituteId+"_"
        }
               sql += " and u.username=qrm.username group by u.username,u.name,u.profilepic,u.id,institute_id  order by userPoints desc limit 10"
         def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        int i=0
        int numberOfRanks = results.size()
        int lowestPoints = 0;
        String state
        int defaultInstituteId= -1
        KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("demoInstituteId",siteId)
        if(keyValueMst!=null){
            defaultInstituteId = Integer.parseInt(keyValueMst.keyValue)
        }
        List rankUsers = results.collect { rank ->
            User user = dataProviderService.getUserMst(rank.username)
            UserPointsPrepJoy userPoints = UserPointsPrepJoy.findByUsernameAndPointsTypeAndSiteId(rank.userName,"prepjoy",siteId)

            i++;
            lowestPoints = Integer.parseInt(""+rank.userPoints).intValue()
            state = user.state
            if(rank.institute_id!=null&&instituteId==null&&rank.institute_id.intValue()!=defaultInstituteId) {
                state = dataProviderService.getInstituteMst(rank.institute_id).name
            }
            return [rank:i, name:rank.name, username:utilService.userManagementService.encrypt(rank.username),
                    userPoints: rank.userPoints,profilePic:rank.profilepic, userId:rank.id,
                    state:state,memberSince:convertDate(user.dateCreated,"UTC","IST"),
                    currentBadge: userPoints!=null?userPoints.currentBadge:null]
        }

        if(results.size()>0) {
            Gson gson = new Gson();
            String element = gson.toJson(rankUsers, new TypeToken<List>() {}.getType())
            redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + todaysDate) = element
        }else{
            redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + todaysDate) = "No Ranks"
        }
        redisService.(institutePrefix+"prepJoyWeeklyRank_noOfRanks_"+siteId+"_"+todaysDate) = ""+numberOfRanks
        redisService.(institutePrefix+"prepJoyWeeklyRank_lowestPoints_"+siteId+"_"+todaysDate) = ""+lowestPoints

    }

    def getMonthlyLeaderBoard(todaysDate,Integer siteId, String instituteId){
        String institutePrefix=""
        def sql = "SELECT sum(qrm.points) userPoints, u.name,u.username,u.profilepic,u.id,COALESCE(qrm.institute_id,null) institute_id FROM wsuser.quiz_rec_mst qrm,user u where " +
                "                date(DATE_ADD(qrm.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+todaysDate+"','%Y-%m-%d')" +
                "               and date(DATE_ADD(qrm.date_created, INTERVAL '5:30' HOUR_MINUTE)) >  DATE_SUB(STR_TO_DATE('"+todaysDate+"','%Y-%m-%d'), INTERVAL 30 DAY)"+
        " and qrm.site_id="+siteId;

        if(instituteId!=null) {
            sql+= " and qrm.institute_id="+instituteId
            institutePrefix="institute_"+instituteId+"_"
        }
        sql+= " and u.username=qrm.username group by u.username,u.name,u.profilepic,u.id,institute_id  order by userPoints desc limit 10"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        int i=0
        int numberOfRanks = results.size()
        int lowestPoints = 0;
        String state
        int defaultInstituteId= -1
        KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("demoInstituteId",siteId)
        if(keyValueMst!=null){
            defaultInstituteId = Integer.parseInt(keyValueMst.keyValue)
        }
        List rankUsers = results.collect { rank ->
            User user = dataProviderService.getUserMst(rank.username)
            UserPointsPrepJoy userPoints = UserPointsPrepJoy.findByUsernameAndPointsTypeAndSiteId(rank.userName,"prepjoy",siteId)

            i++;
            lowestPoints = Integer.parseInt(""+rank.userPoints).intValue()
            state = user.state
            if(rank.institute_id!=null&&instituteId==null&&rank.institute_id.intValue()!=defaultInstituteId) {
                state = dataProviderService.getInstituteMst(rank.institute_id).name
            }
            return [rank:i, name:rank.name, username:utilService.userManagementService.encrypt(rank.username),
                    userPoints: rank.userPoints,profilePic:rank.profilepic, userId:rank.id,
                    state:state,memberSince:convertDate(user.dateCreated,"UTC","IST"),
                    currentBadge: userPoints!=null?userPoints.currentBadge:null]
        }

        if(results.size()>0) {
            Gson gson = new Gson();
            String element = gson.toJson(rankUsers, new TypeToken<List>() {}.getType())
            redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + todaysDate) = element
        }else{
            redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + todaysDate) = "No Ranks"
        }
        redisService.(institutePrefix+"prepJoyMonthlyRank_noOfRanks_"+siteId+"_"+todaysDate) = ""+numberOfRanks
        redisService.(institutePrefix+"prepJoyMonthlyRank_lowestPoints_"+siteId+"_"+todaysDate) = ""+lowestPoints

    }
    def getMultiDaysQuiz(String dateInput, String noOfQuestions, String noOfDays,String currentAffairsType){
        String sql = "select group_concat(res_link) from resource_dtl where res_sub_type='Current Affairs' and res_type='Multiple Choice Questions' and current_affairs_type='"+currentAffairsType+"' " +
                "               and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+dateInput+"','%d-%m-%Y')" +
                "               and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <  DATE_ADD(STR_TO_DATE('"+dateInput+"','%d-%m-%Y'), INTERVAL "+noOfDays+" DAY) "

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String objIds = results[0][0]
        sql = "SELECT t1.* FROM objective_mst AS t1 JOIN (SELECT id FROM objective_mst  where quiz_id in" +
                "                         ("+objIds+") ORDER BY RAND() LIMIT "+noOfQuestions+") as t2 ON t1.id=t2.id"

         dataSource = grailsApplication.mainContext.getBean('dataSource')
         sql1 = new SafeSql(dataSource)
         results = sql1.rows(sql)
        String directions,section
        List jsonAnswers = results.collect { quiz ->
            int noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;

            directions = quiz.directions
            section = quiz.section


            return [id               : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType          : quiz.quiz_type, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                    directions       : quiz.directions, section: quiz.section,
                    answerDescription: quiz.answer_description, answer: (quiz.answer != null ? new String(quiz.answer, "UTF-8") : ""), subject: quiz.subject,
                    chapterId        : quiz.chapter, quizId: quiz.quiz_id, marks: quiz.marks, negativeMarks: quiz.negative_marks, explainLink: quiz.explain_link,
                    startTime: quiz.start_time, endTime: quiz.end_time]
        }

        Gson gson = new Gson();
        String element = gson.toJson(jsonAnswers,new TypeToken<List>() {}.getType())
        redisService.("multiDaysCurrentaffairs_"+dateInput+"_"+noOfQuestions+"_"+noOfDays+"_"+currentAffairsType) = element


    }

    def getWeeklyCurrentAffairsReadingMaterials(String inputDate,String currentAffairsType){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = "SELECT id,title,description,resource_type,show_full_details, " +
                " COALESCE(reference_link,''),COALESCE(answer,''),show_answer,COALESCE(video_link,''),COALESCE(deep_link,''), DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y'), " +
                " COALESCE(level,''),COALESCE( syllabus,''),COALESCE( grade,''),COALESCE( subject,''),COALESCE( plain_description,''),COALESCE( language,'') " +
                " FROM information_mst im WHERE resource_type= 'CURRENT AFFAIRS' " +
                " and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+inputDate+"','%d-%m-%Y')" +
                " and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <  DATE_ADD(STR_TO_DATE('"+inputDate+"','%d-%m-%Y'), INTERVAL 7 DAY)" +
                " and current_affairs_type='"+currentAffairsType+"' "
                " order by id desc  "
        def results = sql1.rows(sql).collect{info->
            return [
                    id:info[0], title: info[1], description: info[2], resourceType: info[3], showFullDetails: info[4],
                    referenceLink: info[5], answer: info[6],showAnswer: info[7],videoLink: info[8],deepLink: info[9], dateCreated: info[10],
                    tag:[level: info[11],syllabus: info[12],grade: info[13],subject: info[14],plainDescription:info[15],language:info[16]],
            ]
        }

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("weeklyCurrentAffairsRead_"+inputDate+"_"+currentAffairsType) = element
    }

    Date convertDate(Date dateFrom, String fromTimeZone, String toTimeZone) throws ParseException {
        String pattern = "yyyy/MM/dd HH:mm:ss";
        SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern);
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone));

        SimpleDateFormat sdfTo = new SimpleDateFormat (pattern);
        sdfTo.setTimeZone(TimeZone.getTimeZone(toTimeZone));
        Date dateTo = sdfFrom.parse(sdfTo.format(dateFrom));
        return dateTo;
    }

    def createDailyTests(Long dailyTestId){

        DailyTestsMst dailyTestsMst  = DailyTestsMst.findById(dailyTestId)
        String bookIds = dailyTestsMst.bookIds
        def sql = "SELECT t1.id FROM chapters_mst AS t1 JOIN (SELECT id FROM chapters_mst  where book_id in" +
                " ("+bookIds+") ORDER BY RAND() LIMIT 10) as t2 ON t1.id=t2.id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        String chapterIds = ""
        results.each { chapter ->
            chapterIds +=""+chapter[0]+","
        }
        chapterIds = chapterIds.substring(0,chapterIds.length()-1)
        sql = "select res_link,chapter_id,cm.name chapterName,bm.title from resource_dtl rd,chapters_mst cm,books_mst bm  where res_type='Multiple Choice Questions' and " +
                "chapter_id in (" + chapterIds + ")  and cm.id=rd.chapter_id" +
                "  and bm.id=cm.book_id   and rd.res_link is not null      ";
        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        def quizIdsList = sql1.rows(sql);
        def quizIds = [];
        String objIds = "";
        String stringQuizIds="";
        quizIdsList.each { quiz ->
            try {
                quizIds << new Integer("" + quiz[0])
                stringQuizIds += quiz[0] + ","
            }catch(Exception e){
                //nothing to do right now
            }
        }
        stringQuizIds = stringQuizIds.substring(0,stringQuizIds.length()-1)

        sql = "SELECT t1.id FROM objective_mst AS t1 JOIN (SELECT id FROM objective_mst  where quiz_id in" +
                " ("+stringQuizIds+") ORDER BY RAND() LIMIT "+dailyTestsMst.noOfQuestions+") as t2 ON t1.id=t2.id"

        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        def quizList = sql1.rows(sql);
        quizList.each { quiz ->
            objIds +="" + quiz[0]+","
        }
        objIds = objIds.substring(0,objIds.length()-1)
        DailyTestsDtl dailyTestsDtl = new DailyTestsDtl(dailyTestId: dailyTestsMst.id,objIds:objIds)
        dailyTestsDtl.save(failOnError: true, flush: true)
        getDailyTestsLatestAndStartDates(""+dailyTestId)

    }

    def getDailyTestsLatestAndStartDates(String dailyTestId){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = "SELECT date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) as date_created FROM daily_tests_dtl where daily_test_id="+dailyTestId+
         " order by date_created desc" +
                " limit 1"
        def results = sql1.rows(sql);
        def latestDate = results[0][0];

        sql = "SELECT date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) as date_created FROM daily_tests_dtl where daily_test_id="+dailyTestId+
                " order by date_created asc" +
                " limit 1"
        results = sql1.rows(sql);

        def startingDate = results[0][0]
        redisService.("dailyTestsLatestDate"+"_"+dailyTestId) = latestDate
        redisService.("dailyTestStartingDate"+"_"+dailyTestId) = startingDate
    }

    def getDailyTestListForSite(String siteId){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = "SELECT daily_test_id dailyTestId, test_name testName,COALESCE(subject,'') subject,COALESCE(language,'') language,COALESCE(exam_group,'') examGroup," +
                "COALESCE(level,'') level,COALESCE(syllabus,'') syllabus,COALESCE(grade,'') grade " +
                " from daily_tests_site_dtl dtsd,daily_tests_mst dtm where dtsd.site_id="+siteId+" and dtm.id=dtsd.daily_test_id order by examGroup,dtm.test_name"
        def results = sql1.rows(sql);

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("dailyTestListForSite_"+siteId) = element

    }

    def getDailyTestTypesForSite(String siteId){

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = " select exam_group examGroup,description,image_src imageSrc,blog_id blogId from daily_exam_group where exam_group in (SELECT distinct(exam_group) examGroup" +
                " from daily_tests_site_dtl dtsd,daily_tests_mst dtm where dtsd.site_id="+siteId+" and dtm.id=dtsd.daily_test_id) order by examGroup"
        def results = sql1.rows(sql);
        String description=""
        List mockTests = results.collect { tests ->
            description=tests.description
            if(tests.blogId!=null) {
                Blogs blogs = Blogs.findById(tests.blogId)
                description = blogs.colValue
            }
            return [examGroup: tests.examGroup,description: description,imageSrc:tests.imageSrc,blogId:tests.blogId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(mockTests,new TypeToken<List>() {}.getType())


        redisService.("dailyTestTypesForSite_"+siteId) = element

    }

    def getTestsOfExamGroup(String examGroup){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = " select id,test_name testName, level,syllabus,grade,subject,COALESCE(test_details,'') testDetails,test_subject testSubject from daily_tests_mst dtm where  lower(dtm.exam_group)='"+examGroup+"' order by testName"
       def results = sql1.rows(sql);

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("dailyTestsForExamGroup_"+examGroup.replace(' ','-').toLowerCase()) = element

    }

    def getDailyTests(String inputDate, String dailyTestId){

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = "SELECT obj_ids,dtd.id from daily_tests_dtl dtd where dtd.daily_test_id="+dailyTestId+
                " and date(DATE_ADD(dtd.date_created, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('"+inputDate+"','%d-%m-%Y')"

        def results = sql1.rows(sql);
        String objIds = results[0][0]
        String realDailyTestDtlId = ""+results[0][1]

        sql = "SELECT * FROM objective_mst   where id in " + "("+objIds+") "

        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)
        String directions,section
        List jsonAnswers = results.collect { quiz ->
            int noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;

            directions = quiz.directions
            section = quiz.section


            return [id               : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType          : quiz.quiz_type, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                    directions       : quiz.directions, section: quiz.section,
                    answerDescription: quiz.answer_description, answer: (quiz.answer != null ? new String(quiz.answer, "UTF-8") : ""), subject: quiz.subject,
                    chapterId        : quiz.chapter, quizId: quiz.quiz_id, marks: quiz.marks, negativeMarks: quiz.negative_marks, explainLink: quiz.explain_link,
                    startTime: quiz.start_time, endTime: quiz.end_time]
        }




        Gson gson = new Gson();
        String element = gson.toJson(jsonAnswers,new TypeToken<List>() {}.getType())
        redisService.("dailyTests_"+inputDate+"_"+dailyTestId) = element
        redisService.("dailyTestsDtlId_"+inputDate+"_"+dailyTestId) = realDailyTestDtlId

    }

    def getUserAnalytics(String username){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = "select match_status, count(match_status) matchCount from quiz_rec_mst where match_status is not null and username='"+username+"' group by match_status "

        def results = sql1.rows(sql);
    }

    def getSiteLevelBooks(String siteId){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        //for sql data
        sql = "select book_id,name from site_level_books where site_id="+siteId

        def results = sql1.rows(sql);
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("siteLevelBooks_"+siteId) = element
    }

    def getFullQuizDetails(quizRecMstId, liveMockMstId = null){
        boolean canSeeAnswers = false
        String username = springSecurityService.currentUser.username
        QuizRecMst quizRecMst = QuizRecMst.findById(new Integer(quizRecMstId))
        if(quizRecMst.username.equals(username)) canSeeAnswers = true
        if(!canSeeAnswers){
            //check if it is testGenId is not null
            if(quizRecMst.testGenId!=null){
               TestsShared testsShared = TestsShared.findByTestIdAndBatchIdIsNotNull(quizRecMst.testGenId,username)
                if(testsShared!=null) {
                   CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(testsShared.batchId)
                    if(courseBatchesDtl!=null) {
                        //get default batch id
                         def defaultBatchId = instManagerService.getDefaultBatchId(courseBatchesDtl.conductedBy)
                          BatchUserDtl batchUserDtl = BatchUserDtl.findByBatchIdAndUsernameAndUserTypeInList(defaultBatchId,username,['Instructor','Manager'])
                        if(batchUserDtl!=null) canSeeAnswers = true
                    }

                }
            }
        }
        if(canSeeAnswers) {
            String sql
            String level, syllabus, grade, subject
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new SafeSql(dataSource)
            ExamMst examMst = null
            def examDtl = null
            List ranks = null
            def currentUserRank = null
            String quizName = ""

            //for sql data
            sql = "select * from quiz_rec_dtl where quiz_rec_id=" + quizRecMstId + " order by id"
            def results = sql1.rows(sql);
            String objIds = ""
            String firstObjId = null
            List userAnswers = results.collect { userAnswer ->
                objIds += userAnswer.obj_id + ","
                if (firstObjId == null) firstObjId = userAnswer.obj_id
                return [id      : userAnswer.obj_id, userOption: userAnswer.user_option, botOption: userAnswer.challenger_option,
                        userTime: userAnswer.user_time, botTime: userAnswer.challenger_time, correctOption: userAnswer.correct_option, reviewedQ: userAnswer.marked_for_review]

            }
            objIds = objIds.substring(0, objIds.length() - 1)

            if (quizRecMst.dailyTestDtlId != null) {
                DailyTestsMst dailyTestsMst = getDailyTestMst(quizRecMst.dailyTestDtlId)
                level = dailyTestsMst.level
                syllabus = dailyTestsMst.syllabus
                grade = dailyTestsMst.grade
                subject = dailyTestsMst.testSubject

            } else if (quizRecMst.resId != null && !"".equals(("" + quizRecMst.resId).trim())) {

                def ranksResult = getQuizRanks("" + quizRecMst.resId, liveMockMstId)
                ranks = ranksResult.ranks
                currentUserRank = ranksResult.currentUserRank
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(quizRecMst.resId)
                if (resourceDtl != null) {
                    quizName = resourceDtl.resourceName
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                    if (chaptersMst != null) {
                        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
                        if (booksTagDtl != null) {
                            level = booksTagDtl.level
                            syllabus = booksTagDtl.syllabus
                            grade = booksTagDtl.grade
                            subject = booksTagDtl.subject

                        }
                    }
                    if (resourceDtl.examId != null) {
                        examMst = ExamMst.findById(resourceDtl.examId)
                        if (examMst != null) {
                            if (redisService.("examDtl_" + examMst.id) == null) dataProviderService.getExamDtls(examMst.id)
                            examDtl = redisService.("examDtl_" + examMst.id)
                        }
                    }
                }
            } else {
                ObjectiveMst objectiveMst = logsService.getObjectiveMst(firstObjId)
                if (objectiveMst != null) {
                    ResourceDtl resourceDtl = logsService.getResourceDtl(objectiveMst.quizId)
                    if (resourceDtl != null) {
                        quizName = resourceDtl.resourceName
                        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                        if (chaptersMst != null) {
                            BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
                            if (booksTagDtl != null) {
                                level = booksTagDtl.level
                                syllabus = booksTagDtl.syllabus
                                grade = booksTagDtl.grade
                                subject = booksTagDtl.subject
                            }
                        }
                        if (resourceDtl.examId != null) {
                            examMst = ExamMst.findById(resourceDtl.examId)
                            if (examMst != null) {
                                if (redisService.("examDtl_" + examMst.id) == null) dataProviderService.getExamDtls(examMst.id)
                                examDtl = redisService.("examDtl_" + examMst.id)
                            }
                        }
                    }
                }
            }


            sql = "SELECT * FROM objective_mst   where id in " + "(" + objIds + ") "


            dataSource = grailsApplication.mainContext.getBean('dataSource')
            sql1 = new SafeSql(dataSource)
            results = sql1.rows(sql)
            String directions, section


            List quizStatisticsList = QuizStatistics.findAllByObjIdInList(Arrays.asList(objIds.split(",")))
            List jsonAnswers = results.collect { quiz ->

                int noOfAnswers = 0;
                if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;

                directions = quiz.directions
                section = quiz.section


                return [id               : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                        resType          : quiz.quiz_type, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                        directions       : quiz.directions, section: quiz.section,
                        answerDescription: quiz.answer_description, answer: (quiz.answer != null ? new String(quiz.answer, "UTF-8") : ""), subject: quiz.subject,
                        chapterId        : quiz.chapter, quizId: quiz.quiz_id, marks: quiz.marks, negativeMarks: quiz.negative_marks, explainLink: quiz.explain_link,
                        startTime        : quiz.start_time, endTime: quiz.end_time, resId: quizRecMst.resId]
            }

            def json = ['status':"OK",'results'     : jsonAnswers, 'userAnswers': userAnswers, matchStatus: quizRecMst.matchStatus, userTime: quizRecMst.userTime,
                        botTime       : quizRecMst.challengerTime, userPoints: quizRecMst.points, botPoints: quizRecMst.challengerPoints, botName: quizRecMst.challenger,
                        language      : quizRecMst.language, siteId: quizRecMst.siteId, source: quizRecMst.source, quizType: quizRecMst.quizType, noOfQuestions: quizRecMst.noOfQuestions,
                        dailyTestDtlId: quizRecMst.dailyTestDtlId,
                        level         : level, syllabus: syllabus, grade: grade, subject: subject, quizRecId: quizRecMst.id, 'examMst': examMst, 'examDtl': examDtl, ranks: ranks, currentUserRank: currentUserRank, quizStatisticsList: quizStatisticsList, resId: quizRecMst.resId, quizId: quizRecMst.quizId, quizName: quizName]
            return json
        }else{
            def json = ['status':"Not Allowed"]
            return json
        }
    }


    def getDailyTestMst(dailyTestId){
        DailyTestsMst dailyTestsMst = redisService.memoizeDomainObject(DailyTestsMst, "dailyTestMst_"+dailyTestId) {
            return DailyTestsMst.findById(new Integer(dailyTestId))
        }

        return dailyTestsMst
    }

    def getRetestDetails(String parentQuizRecId, String options){

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)

        sql = "select group_concat(obj_id) from quiz_rec_dtl where quiz_rec_id="+parentQuizRecId+" and (1<>1"
        if(options.indexOf("skipped")>-1) sql +=" or user_option=-1"
        if(options.indexOf("correct")>-1) sql +=" or user_option=correct_option"
        if(options.indexOf("wrong")>-1) sql +=" or (user_option<>correct_option and user_option!=-1)"
        sql+=")"
        def results = sql1.rows(sql);

        String objId

        //for sql data

        String objIds = results[0][0]


        sql = "SELECT * FROM objective_mst   where id in " + "("+objIds+") "

        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)
        String directions,section
        List jsonAnswers = results.collect { quiz ->
            int noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;

            directions = quiz.directions
            section = quiz.section


            return [id               : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType          : quiz.quiz_type, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                    directions       : quiz.directions, section: quiz.section,
                    answerDescription: quiz.answer_description, answer: (quiz.answer != null ? new String(quiz.answer, "UTF-8") : ""), subject: quiz.subject,
                    chapterId        : quiz.chapter, quizId: quiz.quiz_id, marks: quiz.marks, negativeMarks: quiz.negative_marks, explainLink: quiz.explain_link,
                    startTime: quiz.start_time, endTime: quiz.end_time]
        }




        Gson gson = new Gson();
        String element = gson.toJson(jsonAnswers,new TypeToken<List>() {}.getType())
        return element

    }

    def getRetestDetailsForFlashcards(String parentQuizRecId, String options){

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)

        sql = "select group_concat(obj_id) from quiz_rec_dtl where quiz_rec_id="+parentQuizRecId+" and (1<>1"
        if(options.indexOf("skipped")>-1) sql +=" or user_option=-1"
        if(options.indexOf("correct")>-1) sql +=" or user_option=correct_option"
        if(options.indexOf("wrong")>-1) sql +=" or (user_option<>correct_option and user_option!=-1)"
        sql+=")"

      def results = sql1.rows(sql);

        String objId

        //for sql data

        String objIds = results[0][0]


        sql = "SELECT * FROM objective_mst   where id in " + "("+objIds+") "

        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)
        List keyValues = results.collect { mcq ->
            String answer = ""
            if ("Yes".equals(mcq.answer1)) answer = mcq.option1
            else if ("Yes".equals(mcq.answer2)) answer = mcq.option2
            else if ("Yes".equals(mcq.answer3)) answer = mcq.option3
            else if ("Yes".equals(mcq.answer4)) answer = mcq.option4
            else if ("Yes".equals(mcq.answer5)) answer = mcq.option5

            return [id: mcq.id, term: mcq.question, definition: answer]
        }



        return keyValues

    }

    def getImproveQuestionsForMCQs(String subject, String username,String limit){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)

        sql = "select obj_id,id from quiz_rec_dtl  where  subject='"+subject+"' and username='"+username+"' and ("+
                " user_option=-1"+
                " or user_option<>correct_option "
        sql+=") and latest='true' order by id desc limit "+limit
        def results = sql1.rows(sql);

        String objIds = ""
        results.each { obj ->
            objIds +=obj.obj_id+","
        }
        objIds = objIds.substring(0,objIds.length()-1)
        sql = "SELECT * FROM objective_mst  where id in " + "("+objIds+") "

        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)
        String directions,section
        List jsonAnswers = results.collect { quiz ->
            int noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;

            directions = quiz.directions
            section = quiz.section


            return [id               : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType          : quiz.quiz_type, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                    directions       : quiz.directions, section: quiz.section,
                    answerDescription: quiz.answer_description, answer: (quiz.answer != null ? new String(quiz.answer, "UTF-8") : ""), subject: quiz.subject,
                    chapterId        : quiz.chapter, quizId: quiz.quiz_id, marks: quiz.marks, negativeMarks: quiz.negative_marks, explainLink: quiz.explain_link,
                    startTime: quiz.start_time, endTime: quiz.end_time]
        }




        Gson gson = new Gson();
        String element = gson.toJson(jsonAnswers,new TypeToken<List>() {}.getType())
        return element
    }

    def getImproveQuestions(String subject, String username,String limit){

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)

        sql = "select obj_id,id from quiz_rec_dtl  where  subject='"+subject+"' and username='"+username+"' and ("+
        " user_option=-1"+
        " or user_option<>correct_option "
        sql+=") and latest='true' order by id desc limit "+limit
        def results = sql1.rows(sql);

        String objIds = ""
        results.each { obj ->
            objIds +=obj.obj_id+","
        }
        objIds = objIds.substring(0,objIds.length()-1)
        sql = "SELECT * FROM objective_mst   where id in " + "("+objIds+") "

        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)
        List keyValues = results.collect { mcq ->
            String answer = ""
            if ("Yes".equals(mcq.answer1)) answer = mcq.option1
            else if ("Yes".equals(mcq.answer2)) answer = mcq.option2
            else if ("Yes".equals(mcq.answer3)) answer = mcq.option3
            else if ("Yes".equals(mcq.answer4)) answer = mcq.option4
            else if ("Yes".equals(mcq.answer5)) answer = mcq.option5

            return [id: mcq.id, term: mcq.question, definition: answer]
        }
        return keyValues
    }


    def getQuizRanks(resId, liveMockMstId = null){
        if(resId!=null&&!"".equals(resId)&&!"null".equals(resId)) {
            List<QuizRanks> currentRanks
            if(liveMockMstId != null && !"".equals(liveMockMstId) && !"null".equals(liveMockMstId)) {
                currentRanks = QuizRanks.findAllByResIdAndLiveMockMstId(new Integer(resId), new Integer(liveMockMstId), [sort: 'userRank', order: 'asc'])
            }
            List ranks = currentRanks.collect { rank ->
                User user = dataProviderService.getUserMst(rank.username)
                return [rank: rank.userRank, name: user.name, score: rank.score, userTime: rank.userTime, profilePic: user.profilepic, userId: user.id]
            }

            // Get current user's rank and totalAttempts
            String currentUsername = springSecurityService.currentUser.username
            def currentUserRank = null
            String totalAttempts = "0"

            // Find current user's rank in the ranks list
            QuizRanks userQuizRank = currentRanks.find { it.username == currentUsername }
            if (userQuizRank) {
                User currentUser = dataProviderService.getUserMst(currentUsername)
                currentUserRank = [
                    rank: userQuizRank.userRank,
                    name: currentUser.name,
                    score: userQuizRank.score,
                    userTime: userQuizRank.userTime,
                    profilePic: currentUser.profilepic,
                    userId: currentUser.id
                ]

                // Get totalAttempts from LiveMockMst if liveMockMstId is provided
                if(liveMockMstId != null && !"".equals(liveMockMstId) && !"null".equals(liveMockMstId)) {
                    LiveMockMst liveMockMst = LiveMockMst.findById(new Long(liveMockMstId))
                    if(liveMockMst && liveMockMst.totalAttempts) {
                        totalAttempts = liveMockMst.totalAttempts
                    }
                }
                currentUserRank.totalAttempts = totalAttempts
            }

            return [ranks: ranks, currentUserRank: currentUserRank]
        }else return [ranks: [], currentUserRank: null]

    }

}
