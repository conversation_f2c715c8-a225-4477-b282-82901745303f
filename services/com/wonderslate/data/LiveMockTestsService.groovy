package com.wonderslate.data

import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.sqlutil.SafeSql
import grails.transaction.Transactional

@Transactional
class LiveMockTestsService {
    UserManagementService userManagementService
    def grailsApplication
    /**
     * Common method to get quiz details from ResourceDtl using mcqResId
     */
    private def getQuizDetails(Long mcqResId, def session, def request, def response) {
        try {
            ResourceDtl resourceDtl = ResourceDtl.findById(mcqResId)
            if (!resourceDtl) {
                return null
            }

            // Count MCQs using resLink (quiz_id in ObjectiveMst)
            Integer mcqCount = 0
            if (resourceDtl.resLink) {
                mcqCount = ObjectiveMst.countByQuizId(Integer.parseInt(resourceDtl.resLink))
            }

            boolean hasTestAccess = userManagementService.canSeeResourceCheck(resourceDtl, session, request, response)

            return [
                resourceDtl: resourceDtl,
                mcqCount: mcqCount,
                hasTestAccess: hasTestAccess
            ]
        } catch (Exception e) {
            log.error("Error getting quiz details for mcqResId ${mcqResId}: ${e.message}", e)
            return null
        }
    }

    /**
     * Get all ongoing mock tests with pagination
     * A test is ongoing if current date is between test start date and test end date
     */
    def getOngoingMockTests(Map paginationParams = [:], def session, def request, def response, Integer siteId) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // Get total count first
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND rd.testStartDate IS NOT NULL
                AND rd.testEndDate IS NOT NULL
                AND rd.testStartDate <= :currentDate
                AND rd.testEndDate > :currentDate
            """, [currentDate: currentDate, siteId: siteId])[0]

            // Get paginated results
            def ongoingMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND rd.testStartDate IS NOT NULL
                AND rd.testEndDate IS NOT NULL
                AND rd.testStartDate <= :currentDate
                AND rd.testEndDate > :currentDate
                ORDER BY rd.testStartDate DESC
            """, [currentDate: currentDate, siteId: siteId], [max: max, offset: offset])

            def result = []
            ongoingMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl
                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        quizId: resourceDtl.resLink,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        testResultDate: resourceDtl.testResultDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: 'ongoing'
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting ongoing mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch ongoing mock tests']
        }
    }

    /**
     * Get all upcoming mock tests with pagination
     * A test is upcoming if current date is less than test start date
     */
    def getUpcomingMockTests(Map paginationParams = [:], def session, def request, def response, Integer siteId) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // Get total count first
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND rd.testStartDate IS NOT NULL
                AND rd.testStartDate > :currentDate
            """, [currentDate: currentDate, siteId: siteId])[0]

            // Get paginated results
            def upcomingMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND rd.testStartDate IS NOT NULL
                AND rd.testStartDate > :currentDate
                ORDER BY rd.testStartDate ASC
            """, [currentDate: currentDate, siteId: siteId], [max: max, offset: offset])

            def result = []
            upcomingMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl
                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        quizId: resourceDtl.resLink,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        testResultDate: resourceDtl.testResultDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: 'upcoming'
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting upcoming mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch upcoming mock tests']
        }
    }

    /**
     * Get all completed mock tests with pagination
     * A test is completed if test end date is less than current date
     * Now returns only user-specific completed tests based on QuizRecMst records
     */
    def getCompletedMockTests(Map paginationParams = [:], def session, def request, def response, Integer siteId, String username) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // If no username provided, return empty result
            if (!username) {
                println("No username provided")
                return [
                    status: 'success',
                    data: [],
                    pagination: [
                        total: 0,
                        max: max,
                        offset: offset,
                        currentPage: 1,
                        totalPages: 0
                    ]
                ]
            }

            // First, get all tests that the user attempted from QuizRecMst using SQL
            def userQuizRecords = []
            def userResIds = []

            try {
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new SafeSql(dataSource)

                String quizRecSql = "SELECT id, res_id, username FROM quiz_rec_mst WHERE username='"+ username +"' AND res_id IS NOT NULL"

                println(quizRecSql)

                def results = sql1.rows(quizRecSql)

                println(results)

                results.each { row ->
                    userQuizRecords.add([
                        id: row.id,
                        resId: row.res_id,
                        username: row.username
                    ])
                    // Convert Integer to Long to match LiveMockMst.mcqResId type
                    userResIds.add(row.res_id as Long)
                }

            } catch (Exception e) {
                log.error("Error accessing QuizRecMst: ${e.message}", e)
                // If we can't access user quiz records, return empty result
                return [
                    status: 'success',
                    data: [],
                    pagination: [
                        total: 0,
                        max: max,
                        offset: offset,
                        currentPage: 1,
                        totalPages: 0
                    ]
                ]
            }

            println(userResIds)

            if (userResIds.isEmpty()) {
                return [
                    status: 'success',
                    data: [],
                    pagination: [
                        total: 0,
                        max: max,
                        offset: offset,
                        currentPage: 1,
                        totalPages: 0
                    ]
                ]
            }

            // Get total count first - only for tests user attempted and are in LiveMockMst
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND lm.mcqResId IN (:userResIds)
                AND rd.testEndDate IS NOT NULL
                AND rd.testEndDate < :currentDate
            """, [currentDate: currentDate, siteId: siteId, userResIds: userResIds])[0]

            // Get paginated results
            def completedMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND lm.mcqResId IN (:userResIds)
                AND rd.testEndDate IS NOT NULL
                AND rd.testEndDate < :currentDate
                ORDER BY rd.testEndDate DESC
            """, [currentDate: currentDate, siteId: siteId, userResIds: userResIds], [max: max, offset: offset])

            def result = []
            completedMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl

                    // Find the corresponding QuizRecMst record for this user and resId
                    // Convert resId to Long for comparison since mcqResId is Long
                    def userQuizRec = userQuizRecords.find { (it.resId as Long) == liveMockMst.mcqResId }

                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        quizId: resourceDtl.resLink,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        testResultDate: resourceDtl.testResultDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: 'completed',
                        quizRecId: userQuizRec?.id  // Include quizRecId from QuizRecMst
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting completed mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch completed mock tests']
        }
    }

    /**
     * Get all completed mock tests with pagination for admin (no user filtering)
     * A test is completed if test end date is less than current date
     * This method returns all completed tests regardless of user attempts
     */
    def getCompletedMockTestsAdmin(Map paginationParams = [:], def session, def request, def response, Integer siteId) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // Get total count first - all completed tests in LiveMockMst
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND rd.testEndDate IS NOT NULL
                AND rd.testEndDate < :currentDate
            """, [currentDate: currentDate, siteId: siteId])[0]

            // Get paginated results
            def completedMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND lm.siteId = :siteId
                AND rd.testEndDate IS NOT NULL
                AND rd.testEndDate < :currentDate
                ORDER BY rd.testEndDate DESC
            """, [currentDate: currentDate, siteId: siteId], [max: max, offset: offset])

            def result = []
            completedMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl

                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        quizId: resourceDtl.resLink,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        testResultDate: resourceDtl.testResultDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: 'completed'
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting completed mock tests for admin: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch completed mock tests']
        }
    }

    /**
     * Delete a mock test by setting isDeleted to true
     */
    def deleteMockTest(Long mockTestId, String deletedBy) {
        try {
            LiveMockMst liveMockMst = LiveMockMst.get(mockTestId)

            if (!liveMockMst) {
                return [status: 'error', message: 'Mock test not found']
            }

            if (liveMockMst.isDeleted == 'true') {
                return [status: 'error', message: 'Mock test is already deleted']
            }

            liveMockMst.isDeleted = 'true'
            liveMockMst.deletedBy = deletedBy
            liveMockMst.save(failOnError: true, flush: true)

            return [status: 'success', message: 'Mock test deleted successfully']
        } catch (Exception e) {
            log.error("Error deleting mock test: ${e.message}", e)
            return [status: 'error', message: 'Failed to delete mock test']
        }
    }
}
